{"version": 3, "sources": ["../../src/lib/file-exists.ts"], "names": ["fileExists", "FileType", "fileName", "type", "stats", "promises", "stat", "isFile", "isDirectory", "existsSync", "err", "isError", "code"], "mappings": ";;;;;;;;;;;;;;;;;;IAQsBA,UAAU;eAAVA;;;oBARe;gEACjB;;;;;;;UAERC;;;GAAAA,aAAAA;AAKL,eAAeD,WACpBE,QAAgB,EAChBC,IAAe;IAEf,IAAI;QACF,IAAIA,iBAAwB;YAC1B,MAAMC,QAAQ,MAAMC,YAAQ,CAACC,IAAI,CAACJ;YAClC,OAAOE,MAAMG,MAAM;QACrB,OAAO,IAAIJ,sBAA6B;YACtC,MAAMC,QAAQ,MAAMC,YAAQ,CAACC,IAAI,CAACJ;YAClC,OAAOE,MAAMI,WAAW;QAC1B;QAEA,OAAOC,IAAAA,cAAU,EAACP;IACpB,EAAE,OAAOQ,KAAK;QACZ,IACEC,IAAAA,gBAAO,EAACD,QACPA,CAAAA,IAAIE,IAAI,KAAK,YAAYF,IAAIE,IAAI,KAAK,cAAa,GACpD;YACA,OAAO;QACT;QACA,MAAMF;IACR;AACF"}