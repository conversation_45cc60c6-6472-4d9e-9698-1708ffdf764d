{"name": "mcp-tutorial", "version": "0.0.0", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "twilio-run", "deploy": "twilio-run deploy"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "@twilio-alpha/mcp": "^0.5.1", "@twilio/runtime-handler": "2.0.1", "crypto": "^1.0.1", "dotenv": "^16.5.0", "twilio": "^5.6.1", "zod": "^3.25.23"}, "devDependencies": {"twilio-run": "^4.2.0"}, "engines": {"node": "22"}}