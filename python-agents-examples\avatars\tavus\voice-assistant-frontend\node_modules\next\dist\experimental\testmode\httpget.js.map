{"version": 3, "sources": ["../../../src/experimental/testmode/httpget.ts"], "names": ["interceptHttpGet", "originalFetch", "clientRequestInterceptor", "ClientRequestInterceptor", "on", "request", "response", "handleFetch", "respondWith", "apply", "dispose"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;+BALyB;uBACb;AAIrB,SAASA,iBAAiBC,aAAoB;IACnD,MAAMC,2BAA2B,IAAIC,uCAAwB;IAC7DD,yBAAyBE,EAAE,CAAC,WAAW,OAAO,EAAEC,OAAO,EAAE;QACvD,MAAMC,WAAW,MAAMC,IAAAA,kBAAW,EAACN,eAAeI;QAClDA,QAAQG,WAAW,CAACF;IACtB;IACAJ,yBAAyBO,KAAK;IAE9B,WAAW;IACX,OAAO;QACLP,yBAAyBQ,OAAO;IAClC;AACF"}