import logging
import json
import uuid
import os
import yaml
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, List, Dict, Any, TypedDict
from dotenv import load_dotenv
from livekit.agents import JobContext, WorkerOptions, cli, llm, WorkerPermissions, RoomOutputOptions
from livekit.agents.llm import function_tool
from livekit.agents.voice import Agent, AgentSession, RunContext
# from livekit.plugins.turn_detector.english import EnglishModel
from livekit.plugins import openai, silero, deepgram, tavus, elevenlabs, rime, turn_detector
import asyncio

load_dotenv('.env')

logger = logging.getLogger("avatar")
logger.setLevel(logging.INFO)

@dataclass
class UserData:
    """Class to store user data during a session."""
    ctx: Optional[JobContext] = None
    # input_type: Literal["audio", "video", "text"] = "audio"


class AvatarAgent(Agent):
    def __init__(self) -> None:

        chat_ctx = llm.ChatContext()
        
        # Загрузка данных из JSON файла
        presentation_path = Path(__file__).parent / 'presentation.json'
        with open(presentation_path, 'r', encoding='utf-8') as file:
            presentation_data = json.load(file)
        
        # Передача JSON в текст
        chat_ctx.add_message(
            role="system",
            content=json.dumps(presentation_data, ensure_ascii=False, indent=2),
        )

        super().__init__(
            chat_ctx=chat_ctx,
            instructions=load_prompt('prompt.yaml'),
            stt=deepgram.STT(language="ru", model="nova-2"),
            llm=openai.LLM(model="gpt-4.1-nano"),
            # tts=elevenlabs.TTS(
            #     # voice_id="cjVigY5qzO86Huf0OWal"
            #     voice_id="21m00Tcm4TlvDq8ikWAM"
            # ),
            tts=openai.TTS(),
            vad=silero.VAD.load(),
        )

    
    @function_tool
    async def switchSlide(self, context: RunContext[UserData], url: str) -> None:

        userdata = context.userdata

        if not userdata.ctx or not userdata.ctx.room:
            return f"Couldn't access the room!"
        
        room = userdata.ctx.room

        participants = room.remote_participants
        if not participants:
            return f"No participants found!"

        participant = next(iter(participants.values()), None)
        if not participant:
            return f"Couldn't get the participant!"
        
        payload = {
            "type": "image",
            "url": url,
        }
        
        # Make sure payload is properly serialized
        json_payload = json.dumps(payload)
        logger.info(f"Sending switch slide payload: {json_payload}")

        await room.local_participant.perform_rpc(
            destination_identity=participant.identity,
            method="client.switchslide",
            payload=json_payload
        )
        
        return f"I've switched to slide: '{url}'"
       
    
    async def on_enter(self):
        await asyncio.sleep(5)
        self.session.generate_reply()

async def entrypoint(ctx: JobContext):
    agent = AvatarAgent()
    await ctx.connect()

    # Create a single AgentSession with userdata
    userdata = UserData(ctx=ctx)
    session = AgentSession[UserData](
        userdata=userdata, 
        # turn_detection=EnglishModel()
    )

    # Create the avatar session
    # avatar = tavus.AvatarSession(
    #     replica_id="r4c41453d2",
    #     # replica_id="rf4703150052",
    #     persona_id="p2fbd605"
    # )

    # Start the avatar with the same session that has userdata
    # await avatar.start(session, room=ctx.room)

    async def handle_update_settings(rpc_data):
        try:
            logger.info(f"Received update settings payload: {rpc_data}")
            
            # Extract the payload from the RpcInvocationData object
            payload_str = rpc_data.payload
            logger.info(f"Extracted payload string: {payload_str}")
            
            # Parse the JSON payload
            payload_data = json.loads(payload_str)
            logger.info(f"Parsed payload data: {payload_data}")
            
            audio_enabled = payload_data.get("audio")
            
            tracks = ctx.room.local_participant.track_publications
            for _, track in tracks.items():
                if audio_enabled:
                    track.track.unmute()
                else:
                    track.track.mute()

            for _, participant in ctx.room.remote_participants.items():
                for _, p_track in participant.track_publications.items():
                    p_track.set_subscribed(audio_enabled)
                
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing error for payload '{rpc_data.payload}': {e}")
            return f"error: {str(e)}"
        except Exception as e:
            logger.error(f"Error handling update settings: {e}")
            return f"error: {str(e)}"

    logger.info("Registering RPC methods")
    ctx.room.local_participant.register_rpc_method(
        "agent.updateSettings",
        handle_update_settings
    )

    # Start the agent session with the same session object
    await session.start(
        room=ctx.room,
        room_output_options=RoomOutputOptions(
            audio_enabled=True,  # Enable audio since we want the avatar to speak
        ),
        agent=agent
    )

def load_prompt(filename):
    """Load a prompt from a YAML file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    prompt_path = os.path.join(script_dir, filename)
    
    try:
        with open(prompt_path, 'r', encoding='utf-8') as file:
            prompt_data = yaml.safe_load(file)
            return prompt_data.get('instructions', '')
    except (FileNotFoundError, yaml.YAMLError) as e:
        print(f"Error loading prompt file {filename}: {e}")
        return "" 

if __name__ == "__main__":
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint
        )
    )
