import { useChat, useLocalParticipant, useTrackTranscription, useVoiceAssistant } from "@livekit/components-react";
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from "react";
import type { ChatMessage as ComponentsChatMessage } from "@livekit/components-react";
import { LocalParticipant, type Participant, Track, type TranscriptionSegment } from "livekit-client";

type ChatMessageType = {
  name: string;
  message: string;
  isSelf: boolean;
  timestamp: number;
};

type ChatBlockProps = {
  messages: ChatMessageType[];
  onSend?: (message: string) => Promise<ComponentsChatMessage>;
};

type ChatMessageInput = {
  placeholder: string;
  height: number;
  onSend?: (message: string) => void;
};

const inputHeight = 70;

type ChatMessageProps = {
  message: string;
  name: string;
  isSelf: boolean;
  hideName?: boolean;
};

export default function ChatView() {
  const {agentTranscriptions, audioTrack} = useVoiceAssistant();
  const localParticipant = useLocalParticipant();
  const localMessages = useTrackTranscription({
    publication: localParticipant.microphoneTrack,
    source: Track.Source.Microphone,
    participant: localParticipant.localParticipant,
  });

  const [transcripts, setTranscripts] = useState<Map<string, ChatMessageType>>(
    new Map()
  );
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const { chatMessages, send: sendChat } = useChat();

  useEffect(() => {
    if (audioTrack) {
      for (const s of agentTranscriptions) {
        transcripts.set(
          s.id,
          segmentToChatMessage(
            s,
            transcripts.get(s.id),
            audioTrack.participant
          )
        );
      }
    }
    
    for (const s of localMessages.segments) {
      transcripts.set(
        s.id,
        segmentToChatMessage(
          s,
          transcripts.get(s.id),
          localParticipant.localParticipant
        )
      );
    }

    const allMessages = Array.from(transcripts.values());
    for (const msg of chatMessages) {
      const isAgent = audioTrack
        ? msg.from?.identity === audioTrack.participant?.identity
        : msg.from?.identity !== localParticipant.localParticipant.identity;
      const isSelf =
        msg.from?.identity === localParticipant.localParticipant.identity;
      let name = msg.from?.name;
      if (!name) {
        if (isAgent) {
          name = "Agent";
        } else if (isSelf) {
          name = "You";
        } else {
          name = "Unknown";
        }
      }
      allMessages.push({
        name,
        message: msg.message,
        timestamp: msg.timestamp,
        isSelf: isSelf,
      });
    }
    allMessages.sort((a, b) => a.timestamp - b.timestamp);
    setMessages(allMessages);
  }, [
    transcripts,
    chatMessages,
    localParticipant.localParticipant,
    audioTrack?.participant,
    agentTranscriptions,
    localMessages.segments,
    audioTrack,
  ]);
  
  return (
    <div className={"flex flex-col h-full"}>
      <div className={"flex flex-col items-center grow w-full max-h-full"}>
        <ChatBlock messages={messages} onSend={sendChat} />
      </div>
    </div>
  );
}

const ChatBlock = ({ messages, onSend }: ChatBlockProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // biome-ignore lint/correctness/useExhaustiveDependencies: messages are requred for scrolling during streaming
    useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [containerRef, messages]);

  return (
    <div className="flex flex-col gap-4 w-full h-full">
      <div
        ref={containerRef}
        className="flex flex-col-reverse overflow-y-auto "
        style={{
          height: `calc(100% - ${inputHeight}px)`,
        }}
      >
        <div className="flex flex-col h-fit max-h-full justify-end">
          {messages.map((message, index, allMsg) => {
            const hideName =
              index >= 1 && allMsg[index - 1].name === message.name;

            const messageKey = `${message.timestamp}-${message.name}-${index}`;
            return (
              <ChatMessage
                key={messageKey}
                hideName={hideName}
                name={message.name}
                message={message.message}
                isSelf={message.isSelf}
              />
            );
          })}
        </div>
      </div>
      <ChatMessageInput
        height={inputHeight}
        placeholder="Type a message"
        onSend={onSend}
      />
    </div>
  );
};

const ChatMessageInput = ({
  placeholder,
  height,
  onSend,
}: ChatMessageInput) => {
  const [message, setMessage] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [inputHasFocus, setInputHasFocus] = useState(false);

  const handleSend = useCallback(() => {
    if (!onSend) {
      return;
    }
    if (message === "") {
      return;
    }

    onSend(message);
    setMessage("");
  }, [onSend, message]);

  useEffect(() => {
    if (message === "") {
      setIsTyping(false);
      return;
    }
    
    setIsTyping(true);
    const timeout = setTimeout(() => {
      setIsTyping(false);
    }, 500);

    return () => clearTimeout(timeout);
  }, [message]);

  return (
    <div
      className="flex flex-col gap-2 border-t border-t-gray-800"
      style={{ height: height }}
    >
      <div className="flex flex-row pt-3 gap-2 items-center relative">
        <div
          className={`w-2 h-4 bg-${inputHasFocus ? "amber" : "gray"}-${
            inputHasFocus ? 500 : 800
          } ${inputHasFocus ? "shadow-amber" : ""} absolute left-2 ${
            !isTyping && inputHasFocus ? "cursor-animation" : ""
          }`}
        />
        <input
          ref={inputRef}
          className={"w-full text-xs bg-transparent opacity-25 text-gray-300 p-2 pr-6 rounded-sm focus:opacity-100 focus:outline-none focus:border-amber-700 focus:ring-1 focus:ring-amber-700 resize-none"}
          style={{
            paddingLeft: message.length > 0 ? "12px" : "24px",
            caretShape: "block",
          }}
          placeholder={placeholder}
          value={message}
          onChange={(e) => {
            setMessage(e.target.value);
          }}
          onFocus={() => {
            setInputHasFocus(true);
          }}
          onBlur={() => {
            setInputHasFocus(false);
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleSend();
            }
          }}
        />
        <button
          type="button"
          disabled={message.length === 0 || !onSend}
          onClick={handleSend}
          className={`text-xs uppercase text-amber-500 hover:bg-amber-950 p-2 rounded-md opacity-${
            message.length > 0 ? 100 : 25
          } pointer-events-${message.length > 0 ? "auto" : "none"}`}
        >
          Send
        </button>
      </div>
    </div>
  );
};

const ChatMessage = ({
  name,
  message,
  isSelf,
  hideName,
}: ChatMessageProps) => {
  return (
    <div className={`flex flex-col gap-1 ${hideName ? "pt-0" : "pt-6"}`}>
      {!hideName && (
        <div
          className={`text-${
            isSelf ? "gray-700" : "amber-800 text-ts-amber"
          } uppercase text-xs`}
        >
          {name}
        </div>
      )}
      <div
        className={`pr-4 text-${
          isSelf ? "gray-300" : "amber-500"
        } text-sm ${
          isSelf ? "" : "drop-shadow-amber"
        } whitespace-pre-line`}
      >
        {message}
      </div>
    </div>
  );
};

function segmentToChatMessage(
  s: TranscriptionSegment,
  existingMessage: ChatMessageType | undefined,
  participant: Participant
): ChatMessageType {
  const msg: ChatMessageType = {
    message: s.final ? s.text : `${s.text} ...`,
    name: participant instanceof LocalParticipant ? "You" : "Agent",
    isSelf: participant instanceof LocalParticipant,
    timestamp: existingMessage?.timestamp ?? Date.now(),
  };
  return msg;
}