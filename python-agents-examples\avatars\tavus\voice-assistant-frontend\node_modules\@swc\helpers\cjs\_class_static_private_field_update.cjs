"use strict";

var _class_apply_descriptor_update = require("./_class_apply_descriptor_update.cjs");
var _class_check_private_static_access = require("./_class_check_private_static_access.cjs");
var _class_check_private_static_field_descriptor = require("./_class_check_private_static_field_descriptor.cjs");

exports._ = exports._class_static_private_field_update = _class_static_private_field_update;
function _class_static_private_field_update(receiver, classConstructor, descriptor) {
    _class_check_private_static_access._(receiver, classConstructor);
    _class_check_private_static_field_descriptor._(descriptor, "update");

    return _class_apply_descriptor_update._(receiver, descriptor);
}
