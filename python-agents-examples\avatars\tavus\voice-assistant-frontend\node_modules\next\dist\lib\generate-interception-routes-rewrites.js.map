{"version": 3, "sources": ["../../src/lib/generate-interception-routes-rewrites.ts"], "names": ["generateInterceptionRoutesRewrites", "isInterceptionRouteRewrite", "toPathToRegexpPath", "path", "replace", "_", "capture", "startsWith", "slice", "voidParamsBeforeInterceptionMarker", "newPath", "foundInterceptionMarker", "segment", "split", "INTERCEPTION_ROUTE_MARKERS", "find", "marker", "push", "join", "appPaths", "basePath", "rewrites", "appPath", "isInterceptionRouteAppPath", "interceptingRoute", "interceptedRoute", "extractInterceptionRouteInformation", "normalizedInterceptingRoute", "normalizedInterceptedRoute", "normalizedAppPath", "interceptingRouteRegex", "pathToRegexp", "toString", "source", "destination", "has", "type", "key", "NEXT_URL", "value", "route"], "mappings": ";;;;;;;;;;;;;;;IA6CgBA,kCAAkC;eAAlCA;;IA4CAC,0BAA0B;eAA1BA;;;8BAzFa;kCACJ;oCAKlB;AAGP,iIAAiI;AACjI,SAASC,mBAAmBC,IAAY;IACtC,OAAOA,KAAKC,OAAO,CAAC,uBAAuB,CAACC,GAAGC;QAC7C,4EAA4E;QAC5E,IAAIA,QAAQC,UAAU,CAAC,QAAQ;YAC7B,OAAO,CAAC,CAAC,EAAED,QAAQE,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC;QACA,OAAO,MAAMF;IACf;AACF;AAEA,gFAAgF;AAChF,yEAAyE;AACzE,2EAA2E;AAC3E,0EAA0E;AAC1E,SAASG,mCAAmCN,IAAY;IACtD,IAAIO,UAAU,EAAE;IAEhB,IAAIC,0BAA0B;IAC9B,KAAK,MAAMC,WAAWT,KAAKU,KAAK,CAAC,KAAM;QACrC,IACEC,8CAA0B,CAACC,IAAI,CAAC,CAACC,SAAWJ,QAAQL,UAAU,CAACS,UAC/D;YACAL,0BAA0B;QAC5B;QAEA,IAAIC,QAAQL,UAAU,CAAC,QAAQ,CAACI,yBAAyB;YACvDD,QAAQO,IAAI,CAAC;QACf,OAAO;YACLP,QAAQO,IAAI,CAACL;QACf;IACF;IAEA,OAAOF,QAAQQ,IAAI,CAAC;AACtB;AAEO,SAASlB,mCACdmB,QAAkB,EAClBC,WAAW,EAAE;IAEb,MAAMC,WAAsB,EAAE;IAE9B,KAAK,MAAMC,WAAWH,SAAU;QAC9B,IAAII,IAAAA,8CAA0B,EAACD,UAAU;YACvC,MAAM,EAAEE,iBAAiB,EAAEC,gBAAgB,EAAE,GAC3CC,IAAAA,uDAAmC,EAACJ;YAEtC,MAAMK,8BAA8B,CAAC,EACnCH,sBAAsB,MAAMtB,mBAAmBsB,qBAAqB,GACrE,MAAM,CAAC;YAER,MAAMI,6BAA6B1B,mBAAmBuB;YACtD,MAAMI,oBAAoBpB,mCACxBP,mBAAmBoB;YAGrB,qEAAqE;YACrE,4DAA4D;YAC5D,4CAA4C;YAC5C,IAAIQ,yBAAyBC,IAAAA,0BAAY,EAACJ,6BACvCK,QAAQ,GACRxB,KAAK,CAAC,GAAG,CAAC;YAEba,SAASJ,IAAI,CAAC;gBACZgB,QAAQ,CAAC,EAAEb,SAAS,EAAEQ,2BAA2B,CAAC;gBAClDM,aAAa,CAAC,EAAEd,SAAS,EAAES,kBAAkB,CAAC;gBAC9CM,KAAK;oBACH;wBACEC,MAAM;wBACNC,KAAKC,0BAAQ;wBACbC,OAAOT;oBACT;iBACD;YACH;QACF;IACF;IAEA,OAAOT;AACT;AAEO,SAASpB,2BAA2BuC,KAAc;QAEhDA,aAAAA;IADP,0HAA0H;IAC1H,OAAOA,EAAAA,aAAAA,MAAML,GAAG,sBAATK,cAAAA,UAAW,CAAC,EAAE,qBAAdA,YAAgBH,GAAG,MAAKC,0BAAQ;AACzC"}