{"version": 3, "sources": ["../../../src/server/lib/etag.ts"], "names": ["fnv1a52", "generateETag", "str", "len", "length", "i", "t0", "v0", "t1", "v1", "t2", "v2", "t3", "v3", "charCodeAt", "payload", "weak", "prefix", "toString"], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;IACYA,OAAO;eAAPA;;IAoCAC,YAAY;eAAZA;;;AApCN,MAAMD,UAAU,CAACE;IACtB,MAAMC,MAAMD,IAAIE,MAAM;IACtB,IAAIC,IAAI,GACNC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK;IAEP,MAAOR,IAAIF,IAAK;QACdI,MAAML,IAAIY,UAAU,CAACT;QACrBC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVH,MAAMH,MAAM;QACZK,MAAMH,MAAM;QACZD,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVI,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVK,KAAK,AAACD,KAAMF,CAAAA,OAAO,EAAC,IAAM;QAC1BC,KAAKD,KAAK;IACZ;IAEA,OACE,AAACG,CAAAA,KAAK,EAAC,IAAK,kBACZF,KAAK,aACLF,KAAK,QACJF,CAAAA,KAAMM,MAAM,CAAC;AAElB;AAEO,MAAMZ,eAAe,CAACc,SAAiBC,OAAO,KAAK;IACxD,MAAMC,SAASD,OAAO,QAAQ;IAC9B,OACEC,SAASjB,QAAQe,SAASG,QAAQ,CAAC,MAAMH,QAAQX,MAAM,CAACc,QAAQ,CAAC,MAAM;AAE3E"}