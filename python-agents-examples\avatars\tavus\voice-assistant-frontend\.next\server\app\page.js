/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNXb3JrJTVDUHJvamVjdHMlNUMyMDI1JTVDbGl2ZWtpdGRlbW8lNUNweXRob24tYWdlbnRzLWV4YW1wbGVzJTVDYXZhdGFycyU1Q3RhdnVzJTVDdm9pY2UtYXNzaXN0YW50LWZyb250ZW5kJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDV29yayU1Q1Byb2plY3RzJTVDMjAyNSU1Q2xpdmVraXRkZW1vJTVDcHl0aG9uLWFnZW50cy1leGFtcGxlcyU1Q2F2YXRhcnMlNUN0YXZ1cyU1Q3ZvaWNlLWFzc2lzdGFudC1mcm9udGVuZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsd0lBQTJKO0FBQ2xMO0FBQ0Esb0NBQW9DLDhlQUFxVDtBQUN6VjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBLHlCQUF5Qiw0SUFBNko7QUFDdEwsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBLG9DQUFvQyw4ZUFBcVQ7QUFDelY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL3ZvaWNlLWFzc2lzdGFudDIvPzQ1ZmIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV29ya1xcXFxQcm9qZWN0c1xcXFwyMDI1XFxcXGxpdmVraXRkZW1vXFxcXHB5dGhvbi1hZ2VudHMtZXhhbXBsZXNcXFxcYXZhdGFyc1xcXFx0YXZ1c1xcXFx2b2ljZS1hc3Npc3RhbnQtZnJvbnRlbmRcXFxcYXBwXFxcXHBhZ2UudHN4XCIpLCBcIkQ6XFxcXFdvcmtcXFxcUHJvamVjdHNcXFxcMjAyNVxcXFxsaXZla2l0ZGVtb1xcXFxweXRob24tYWdlbnRzLWV4YW1wbGVzXFxcXGF2YXRhcnNcXFxcdGF2dXNcXFxcdm9pY2UtYXNzaXN0YW50LWZyb250ZW5kXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhRDpcXFxcV29ya1xcXFxQcm9qZWN0c1xcXFwyMDI1XFxcXGxpdmVraXRkZW1vXFxcXHB5dGhvbi1hZ2VudHMtZXhhbXBsZXNcXFxcYXZhdGFyc1xcXFx0YXZ1c1xcXFx2b2ljZS1hc3Npc3RhbnQtZnJvbnRlbmRcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcV29ya1xcXFxQcm9qZWN0c1xcXFwyMDI1XFxcXGxpdmVraXRkZW1vXFxcXHB5dGhvbi1hZ2VudHMtZXhhbXBsZXNcXFxcYXZhdGFyc1xcXFx0YXZ1c1xcXFx2b2ljZS1hc3Npc3RhbnQtZnJvbnRlbmRcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiRDpcXFxcV29ya1xcXFxQcm9qZWN0c1xcXFwyMDI1XFxcXGxpdmVraXRkZW1vXFxcXHB5dGhvbi1hZ2VudHMtZXhhbXBsZXNcXFxcYXZhdGFyc1xcXFx0YXZ1c1xcXFx2b2ljZS1hc3Npc3RhbnQtZnJvbnRlbmRcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhRDpcXFxcV29ya1xcXFxQcm9qZWN0c1xcXFwyMDI1XFxcXGxpdmVraXRkZW1vXFxcXHB5dGhvbi1hZ2VudHMtZXhhbXBsZXNcXFxcYXZhdGFyc1xcXFx0YXZ1c1xcXFx2b2ljZS1hc3Npc3RhbnQtZnJvbnRlbmRcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiRDpcXFxcV29ya1xcXFxQcm9qZWN0c1xcXFwyMDI1XFxcXGxpdmVraXRkZW1vXFxcXHB5dGhvbi1hZ2VudHMtZXhhbXBsZXNcXFxcYXZhdGFyc1xcXFx0YXZ1c1xcXFx2b2ljZS1hc3Npc3RhbnQtZnJvbnRlbmRcXFxcYXBwXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNXb3JrJTVDJTVDUHJvamVjdHMlNUMlNUMyMDI1JTVDJTVDbGl2ZWtpdGRlbW8lNUMlNUNweXRob24tYWdlbnRzLWV4YW1wbGVzJTVDJTVDYXZhdGFycyU1QyU1Q3RhdnVzJTVDJTVDdm9pY2UtYXNzaXN0YW50LWZyb250ZW5kJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUEySiIsInNvdXJjZXMiOlsid2VicGFjazovL3ZvaWNlLWFzc2lzdGFudDIvP2NlMTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxXb3JrXFxcXFByb2plY3RzXFxcXDIwMjVcXFxcbGl2ZWtpdGRlbW9cXFxccHl0aG9uLWFnZW50cy1leGFtcGxlc1xcXFxhdmF0YXJzXFxcXHRhdnVzXFxcXHZvaWNlLWFzc2lzdGFudC1mcm9udGVuZFxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Public_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22publicSans400%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5C%40livekit%5C%5Ccomponents-styles%5C%5Cdist%5C%5Cgeneral%5C%5Cindex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Public_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22weight%5C%22%3A%5C%22400%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22publicSans400%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Cnode_modules%5C%5C%40livekit%5C%5Ccomponents-styles%5C%5Cdist%5C%5Cgeneral%5C%5Cindex.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CWork%5C%5CProjects%5C%5C2025%5C%5Clivekitdemo%5C%5Cpython-agents-examples%5C%5Cavatars%5C%5Ctavus%5C%5Cvoice-assistant-frontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_CloseIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/CloseIcon */ \"(ssr)/./components/CloseIcon.tsx\");\n/* harmony import */ var _components_NoAgentNotification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NoAgentNotification */ \"(ssr)/./components/NoAgentNotification.tsx\");\n/* harmony import */ var _components_TranscriptionView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TranscriptionView */ \"(ssr)/./components/TranscriptionView.tsx\");\n/* harmony import */ var _components_FlashCardContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/FlashCardContainer */ \"(ssr)/./components/FlashCardContainer.tsx\");\n/* harmony import */ var _components_QuizContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/QuizContainer */ \"(ssr)/./components/QuizContainer.tsx\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/contexts-CPsnPrz2.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-CWooKGw2.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/components-3NBkfi42.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/prefabs.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! livekit-client */ \"(ssr)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_7__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction Page() {\n    const [room] = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(new livekit_client__WEBPACK_IMPORTED_MODULE_6__.Room());\n    const onConnectButtonClicked = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(async ()=>{\n        // Generate room connection details, including:\n        //   - A random Room name\n        //   - A random Participant name\n        //   - An Access Token to permit the participant to join the room\n        //   - The URL of the LiveKit server to connect to\n        //\n        // In real-world application, you would likely allow the user to specify their\n        // own participant name, and possibly to choose from existing rooms to join.\n        const url = new URL(process.env.NEXT_PUBLIC_CONN_DETAILS_ENDPOINT ?? \"/api/connection-details\", window.location.origin);\n        const response = await fetch(url.toString());\n        const connectionDetailsData = await response.json();\n        await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);\n        await room.localParticipant.setMicrophoneEnabled(true);\n    }, [\n        room\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(()=>{\n        room.on(livekit_client__WEBPACK_IMPORTED_MODULE_6__.RoomEvent.MediaDevicesError, onDeviceFailure);\n        return ()=>{\n            room.off(livekit_client__WEBPACK_IMPORTED_MODULE_6__.RoomEvent.MediaDevicesError, onDeviceFailure);\n        };\n    }, [\n        room\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        \"data-lk-theme\": \"default\",\n        className: \"h-full grid content-center bg-[var(--lk-bg)]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_8__.R.Provider, {\n            value: room,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lk-room-container max-w-[1024px] w-[90vw] mx-auto max-h-[90vh]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleVoiceAssistant, {\n                    onConnectButtonClicked: onConnectButtonClicked\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\nfunction SimpleVoiceAssistant(props) {\n    const { state: agentState } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_9__.V)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n            mode: \"wait\",\n            children: agentState === \"disconnected\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                exit: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: [\n                        0.09,\n                        1.04,\n                        0.245,\n                        1.055\n                    ]\n                },\n                className: \"grid items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    transition: {\n                        duration: 0.3,\n                        delay: 0.1\n                    },\n                    className: \"uppercase px-4 py-2 bg-white text-black rounded-md\",\n                    onClick: ()=>props.onConnectButtonClicked(),\n                    children: \"Start a conversation\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 13\n                }, this)\n            }, \"disconnected\", false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -20\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: [\n                        0.09,\n                        1.04,\n                        0.245,\n                        1.055\n                    ]\n                },\n                className: \"flex flex-col items-center gap-4 h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AgentVisualizer, {}, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TranscriptionView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FlashCardContainer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QuizContainer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ControlBar, {\n                            onConnectButtonClicked: props.onConnectButtonClicked\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.R, {}, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoAgentNotification__WEBPACK_IMPORTED_MODULE_2__.NoAgentNotification, {\n                        state: agentState\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"connected\", true, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\nfunction AgentVisualizer() {\n    const { state: agentState, videoTrack, audioTrack } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_9__.V)();\n    if (videoTrack) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[512px] w-[512px] rounded-lg overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.V, {\n                trackRef: videoTrack\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[300px] w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.B, {\n            state: agentState,\n            barCount: 5,\n            trackRef: audioTrack,\n            className: \"agent-visualizer\",\n            options: {\n                minHeight: 24\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\nfunction ControlBar(props) {\n    const { state: agentState } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_9__.V)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-[60px]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: agentState === \"disconnected\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                    initial: {\n                        opacity: 0,\n                        top: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        top: \"-10px\"\n                    },\n                    transition: {\n                        duration: 1,\n                        ease: [\n                            0.09,\n                            1.04,\n                            0.245,\n                            1.055\n                        ]\n                    },\n                    className: \"uppercase absolute left-1/2 -translate-x-1/2 px-4 py-2 bg-white text-black rounded-md\",\n                    onClick: ()=>props.onConnectButtonClicked(),\n                    children: \"Start a conversation\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                children: agentState !== \"disconnected\" && agentState !== \"connecting\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        top: \"10px\"\n                    },\n                    animate: {\n                        opacity: 1,\n                        top: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        top: \"-10px\"\n                    },\n                    transition: {\n                        duration: 0.4,\n                        ease: [\n                            0.09,\n                            1.04,\n                            0.245,\n                            1.055\n                        ]\n                    },\n                    className: \"flex h-8 absolute left-1/2 -translate-x-1/2  justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_13__.VoiceAssistantControlBar, {\n                            controls: {\n                                leave: false\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_12__.D, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CloseIcon__WEBPACK_IMPORTED_MODULE_1__.CloseIcon, {}, void 0, false, {\n                                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\nfunction onDeviceFailure(error) {\n    console.error(error);\n    alert(\"Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/CloseIcon.tsx":
/*!**********************************!*\
  !*** ./components/CloseIcon.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CloseIcon() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        width: \"16\",\n        height: \"16\",\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3.33398 3.33334L12.6673 12.6667M12.6673 3.33334L3.33398 12.6667\",\n            stroke: \"currentColor\",\n            \"stroke-width\": \"2\",\n            \"stroke-linecap\": \"square\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\CloseIcon.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\CloseIcon.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0Nsb3NlSWNvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLFNBQVNBO0lBQ2QscUJBQ0UsOERBQUNDO1FBQUlDLE9BQU07UUFBS0MsUUFBTztRQUFLQyxTQUFRO1FBQVlDLE1BQUs7UUFBT0MsT0FBTTtrQkFDaEUsNEVBQUNDO1lBQ0NDLEdBQUU7WUFDRkMsUUFBTztZQUNQQyxnQkFBYTtZQUNiQyxrQkFBZTs7Ozs7Ozs7Ozs7QUFJdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vY29tcG9uZW50cy9DbG9zZUljb24udHN4PzBmYmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIENsb3NlSWNvbigpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPHN2ZyB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTZcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCIgZmlsbD1cIm5vbmVcIiB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCI+XHJcbiAgICAgIDxwYXRoXHJcbiAgICAgICAgZD1cIk0zLjMzMzk4IDMuMzMzMzRMMTIuNjY3MyAxMi42NjY3TTEyLjY2NzMgMy4zMzMzNEwzLjMzMzk4IDEyLjY2NjdcIlxyXG4gICAgICAgIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiXHJcbiAgICAgICAgc3Ryb2tlLXdpZHRoPVwiMlwiXHJcbiAgICAgICAgc3Ryb2tlLWxpbmVjYXA9XCJzcXVhcmVcIlxyXG4gICAgICAvPlxyXG4gICAgPC9zdmc+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiQ2xvc2VJY29uIiwic3ZnIiwid2lkdGgiLCJoZWlnaHQiLCJ2aWV3Qm94IiwiZmlsbCIsInhtbG5zIiwicGF0aCIsImQiLCJzdHJva2UiLCJzdHJva2Utd2lkdGgiLCJzdHJva2UtbGluZWNhcCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/CloseIcon.tsx\n");

/***/ }),

/***/ "(ssr)/./components/FlashCard.tsx":
/*!**********************************!*\
  !*** ./components/FlashCard.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction FlashCard({ card, onFlip }) {\n    const [isFlipped, setIsFlipped] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(card.isFlipped || false);\n    // Update local state when card prop changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsFlipped(card.isFlipped || false);\n    }, [\n        card.isFlipped\n    ]);\n    const handleFlip = ()=>{\n        setIsFlipped(!isFlipped);\n        if (onFlip) {\n            onFlip(card.id);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-md mx-auto cursor-pointer\",\n        onClick: handleFlip,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n            mode: \"wait\",\n            initial: false,\n            children: !isFlipped ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"bg-white text-black p-6 rounded-lg shadow-lg\",\n                initial: {\n                    rotateY: 90\n                },\n                animate: {\n                    rotateY: 0\n                },\n                exit: {\n                    rotateY: 90\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"Question\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: card.question\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"front\", true, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n                lineNumber: 38,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"bg-blue-100 text-black p-6 rounded-lg shadow-lg\",\n                initial: {\n                    rotateY: 90\n                },\n                animate: {\n                    rotateY: 0\n                },\n                exit: {\n                    rotateY: 90\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-2\",\n                        children: \"Answer\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: card.answer\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, \"back\", true, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n                lineNumber: 50,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCard.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0ZsYXNoQ2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBd0Q7QUFDWjtBQWM3QixTQUFTSSxVQUFVLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFrQjtJQUNoRSxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR04sK0NBQVFBLENBQUNHLEtBQUtFLFNBQVMsSUFBSTtJQUU3RCw0Q0FBNEM7SUFDNUNKLGdEQUFTQSxDQUFDO1FBQ1JLLGFBQWFILEtBQUtFLFNBQVMsSUFBSTtJQUNqQyxHQUFHO1FBQUNGLEtBQUtFLFNBQVM7S0FBQztJQUVuQixNQUFNRSxhQUFhO1FBQ2pCRCxhQUFhLENBQUNEO1FBQ2QsSUFBSUQsUUFBUTtZQUNWQSxPQUFPRCxLQUFLSyxFQUFFO1FBQ2hCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7UUFDQ0MsV0FBVTtRQUNWQyxTQUFTSjtrQkFFVCw0RUFBQ1IsMERBQWVBO1lBQUNhLE1BQUs7WUFBT0MsU0FBUztzQkFDbkMsQ0FBQ1IsMEJBQ0EsOERBQUNQLGlEQUFNQSxDQUFDVyxHQUFHO2dCQUVUQyxXQUFVO2dCQUNWRyxTQUFTO29CQUFFQyxTQUFTO2dCQUFHO2dCQUN2QkMsU0FBUztvQkFBRUQsU0FBUztnQkFBRTtnQkFDdEJFLE1BQU07b0JBQUVGLFNBQVM7Z0JBQUc7Z0JBQ3BCRyxZQUFZO29CQUFFQyxVQUFVO2dCQUFJOztrQ0FFNUIsOERBQUNDO3dCQUFHVCxXQUFVO2tDQUE2Qjs7Ozs7O2tDQUMzQyw4REFBQ1U7a0NBQUdqQixLQUFLa0IsUUFBUTs7Ozs7OztlQVJiOzs7O3FDQVdOLDhEQUFDdkIsaURBQU1BLENBQUNXLEdBQUc7Z0JBRVRDLFdBQVU7Z0JBQ1ZHLFNBQVM7b0JBQUVDLFNBQVM7Z0JBQUc7Z0JBQ3ZCQyxTQUFTO29CQUFFRCxTQUFTO2dCQUFFO2dCQUN0QkUsTUFBTTtvQkFBRUYsU0FBUztnQkFBRztnQkFDcEJHLFlBQVk7b0JBQUVDLFVBQVU7Z0JBQUk7O2tDQUU1Qiw4REFBQ0M7d0JBQUdULFdBQVU7a0NBQTZCOzs7Ozs7a0NBQzNDLDhEQUFDVTtrQ0FBR2pCLEtBQUttQixNQUFNOzs7Ozs7O2VBUlg7Ozs7Ozs7Ozs7Ozs7OztBQWNoQiIsInNvdXJjZXMiOlsid2VicGFjazovL3ZvaWNlLWFzc2lzdGFudDIvLi9jb21wb25lbnRzL0ZsYXNoQ2FyZC50c3g/Yjc4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gXCJmcmFtZXItbW90aW9uXCI7XHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgRmxhc2hDYXJkRGF0YSB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBxdWVzdGlvbjogc3RyaW5nO1xyXG4gIGFuc3dlcjogc3RyaW5nO1xyXG4gIGlzRmxpcHBlZD86IGJvb2xlYW47XHJcbn1cclxuXHJcbmludGVyZmFjZSBGbGFzaENhcmRQcm9wcyB7XHJcbiAgY2FyZDogRmxhc2hDYXJkRGF0YTtcclxuICBvbkZsaXA/OiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRmxhc2hDYXJkKHsgY2FyZCwgb25GbGlwIH06IEZsYXNoQ2FyZFByb3BzKSB7XHJcbiAgY29uc3QgW2lzRmxpcHBlZCwgc2V0SXNGbGlwcGVkXSA9IHVzZVN0YXRlKGNhcmQuaXNGbGlwcGVkIHx8IGZhbHNlKTtcclxuXHJcbiAgLy8gVXBkYXRlIGxvY2FsIHN0YXRlIHdoZW4gY2FyZCBwcm9wIGNoYW5nZXNcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgc2V0SXNGbGlwcGVkKGNhcmQuaXNGbGlwcGVkIHx8IGZhbHNlKTtcclxuICB9LCBbY2FyZC5pc0ZsaXBwZWRdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRmxpcCA9ICgpID0+IHtcclxuICAgIHNldElzRmxpcHBlZCghaXNGbGlwcGVkKTtcclxuICAgIGlmIChvbkZsaXApIHtcclxuICAgICAgb25GbGlwKGNhcmQuaWQpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IFxyXG4gICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgbWF4LXctbWQgbXgtYXV0byBjdXJzb3ItcG9pbnRlclwiXHJcbiAgICAgIG9uQ2xpY2s9e2hhbmRsZUZsaXB9XHJcbiAgICA+XHJcbiAgICAgIDxBbmltYXRlUHJlc2VuY2UgbW9kZT1cIndhaXRcIiBpbml0aWFsPXtmYWxzZX0+XHJcbiAgICAgICAgeyFpc0ZsaXBwZWQgPyAoXHJcbiAgICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgICBrZXk9XCJmcm9udFwiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIHRleHQtYmxhY2sgcC02IHJvdW5kZWQtbGcgc2hhZG93LWxnXCJcclxuICAgICAgICAgICAgaW5pdGlhbD17eyByb3RhdGVZOiA5MCB9fVxyXG4gICAgICAgICAgICBhbmltYXRlPXt7IHJvdGF0ZVk6IDAgfX1cclxuICAgICAgICAgICAgZXhpdD17eyByb3RhdGVZOiA5MCB9fVxyXG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjMgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi0yXCI+UXVlc3Rpb248L2gzPlxyXG4gICAgICAgICAgICA8cD57Y2FyZC5xdWVzdGlvbn08L3A+XHJcbiAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgKSA6IChcclxuICAgICAgICAgIDxtb3Rpb24uZGl2XHJcbiAgICAgICAgICAgIGtleT1cImJhY2tcIlxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTEwMCB0ZXh0LWJsYWNrIHAtNiByb3VuZGVkLWxnIHNoYWRvdy1sZ1wiXHJcbiAgICAgICAgICAgIGluaXRpYWw9e3sgcm90YXRlWTogOTAgfX1cclxuICAgICAgICAgICAgYW5pbWF0ZT17eyByb3RhdGVZOiAwIH19XHJcbiAgICAgICAgICAgIGV4aXQ9e3sgcm90YXRlWTogOTAgfX1cclxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC4zIH19XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItMlwiPkFuc3dlcjwvaDM+XHJcbiAgICAgICAgICAgIDxwPntjYXJkLmFuc3dlcn08L3A+XHJcbiAgICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgICAgKX1cclxuICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkZsYXNoQ2FyZCIsImNhcmQiLCJvbkZsaXAiLCJpc0ZsaXBwZWQiLCJzZXRJc0ZsaXBwZWQiLCJoYW5kbGVGbGlwIiwiaWQiLCJkaXYiLCJjbGFzc05hbWUiLCJvbkNsaWNrIiwibW9kZSIsImluaXRpYWwiLCJyb3RhdGVZIiwiYW5pbWF0ZSIsImV4aXQiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJoMyIsInAiLCJxdWVzdGlvbiIsImFuc3dlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/FlashCard.tsx\n");

/***/ }),

/***/ "(ssr)/./components/FlashCardContainer.tsx":
/*!*******************************************!*\
  !*** ./components/FlashCardContainer.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FlashCardContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _FlashCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FlashCard */ \"(ssr)/./components/FlashCard.tsx\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/contexts-CPsnPrz2.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-CWooKGw2.mjs\");\n\n\n\n\n\nfunction FlashCardContainer() {\n    const [flashCards, setFlashCards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCardIndex, setCurrentCardIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const room = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_3__.f)();\n    const { agent } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_4__.V)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!room) return;\n        // Register RPC method to receive flash cards\n        const handleShowFlashCard = async (data)=>{\n            try {\n                console.log(\"Received flashcard RPC data:\", data);\n                // Check for the correct property in the RPC data\n                if (!data || data.payload === undefined) {\n                    console.error(\"Invalid RPC data received:\", data);\n                    return \"Error: Invalid RPC data format\";\n                }\n                console.log(\"Parsing payload:\", data.payload);\n                // Parse the payload string into a JSON object\n                const payload = typeof data.payload === \"string\" ? JSON.parse(data.payload) : data.payload;\n                if (payload.action === \"show\") {\n                    const newCard = {\n                        id: payload.id,\n                        question: payload.question,\n                        answer: payload.answer,\n                        isFlipped: false\n                    };\n                    setFlashCards((prev)=>{\n                        // Check if card with same ID already exists\n                        const exists = prev.some((card)=>card.id === newCard.id);\n                        if (exists) {\n                            return prev.map((card)=>card.id === newCard.id ? newCard : card);\n                        } else {\n                            return [\n                                ...prev,\n                                newCard\n                            ];\n                        }\n                    });\n                    setCurrentCardIndex(payload.index !== undefined ? payload.index : flashCards.length);\n                    setIsVisible(true);\n                } else if (payload.action === \"flip\") {\n                    setFlashCards((prev)=>prev.map((card)=>card.id === payload.id ? {\n                                ...card,\n                                isFlipped: !card.isFlipped\n                            } : card));\n                } else if (payload.action === \"hide\") {\n                    setIsVisible(false);\n                }\n                return \"Success\";\n            } catch (error) {\n                console.error(\"Error processing flash card data:\", error);\n                return \"Error: \" + (error instanceof Error ? error.message : String(error));\n            }\n        };\n        room.localParticipant.registerRpcMethod(\"client.flashcard\", handleShowFlashCard);\n        return ()=>{\n            // Clean up RPC method when component unmounts\n            room.localParticipant.unregisterRpcMethod(\"client.flashcard\");\n        };\n    }, [\n        room,\n        flashCards.length\n    ]);\n    const handleFlip = async (id)=>{\n        try {\n            // Use the agent from the voice assistant hook\n            if (agent) {\n                console.log(`Sending flip request to agent ${agent.identity} for card ID: ${id}`);\n                // Use the correct RPC pattern and ensure proper payload formatting\n                const result = await room.localParticipant.performRpc({\n                    destinationIdentity: agent.identity,\n                    method: \"agent.flipFlashCard\",\n                    payload: JSON.stringify({\n                        id\n                    })\n                });\n                console.log(`RPC call result: ${result}`);\n                // Also update the local state for immediate feedback\n                setFlashCards((prev)=>prev.map((card)=>card.id === id ? {\n                            ...card,\n                            isFlipped: !card.isFlipped\n                        } : card));\n            } else {\n                console.error(\"Agent not found in the room\");\n            }\n        } catch (error) {\n            console.error(\"Error flipping card:\", error);\n            if (error instanceof Error) {\n                console.error(error.stack);\n            }\n        }\n    };\n    const currentCard = currentCardIndex !== null && flashCards[currentCardIndex] ? flashCards[currentCardIndex] : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n        children: isVisible && currentCard && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n            initial: {\n                opacity: 0,\n                x: 100\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            },\n            exit: {\n                opacity: 0,\n                x: 100\n            },\n            className: \"fixed right-8 top-1/4 w-80 bg-gray-900 p-4 rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold\",\n                            children: \"Flash Card\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVisible(false),\n                            className: \"text-gray-400 hover:text-white\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashCard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    card: currentCard,\n                    onFlip: handleFlip\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, this),\n                flashCards.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentCardIndex((prev)=>prev !== null ? Math.max(0, prev - 1) : 0),\n                            disabled: currentCardIndex === 0,\n                            className: \"px-3 py-1 bg-blue-600 rounded disabled:opacity-50\",\n                            children: \"Previous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                (currentCardIndex ?? 0) + 1,\n                                \" / \",\n                                flashCards.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentCardIndex((prev)=>prev !== null ? Math.min(flashCards.length - 1, prev + 1) : 0),\n                            disabled: currentCardIndex === flashCards.length - 1,\n                            className: \"px-3 py-1 bg-blue-600 rounded disabled:opacity-50\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 13\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n            lineNumber: 127,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\FlashCardContainer.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/FlashCardContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/NoAgentNotification.tsx":
/*!********************************************!*\
  !*** ./components/NoAgentNotification.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoAgentNotification: () => (/* binding */ NoAgentNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\r\n * Renders some user info when no agent connects to the room after a certain time.\r\n */ function NoAgentNotification(props) {\n    const timeToWaitMs = 10000;\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [showNotification, setShowNotification] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const agentHasConnected = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // If the agent has connected, we don't need to show the notification.\n    if ([\n        \"listening\",\n        \"thinking\",\n        \"speaking\"\n    ].includes(props.state) && agentHasConnected.current == false) {\n        agentHasConnected.current = true;\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (props.state === \"connecting\") {\n            timeoutRef.current = window.setTimeout(()=>{\n                if (props.state === \"connecting\" && agentHasConnected.current === false) {\n                    setShowNotification(true);\n                }\n            }, timeToWaitMs);\n        } else {\n            if (timeoutRef.current) {\n                window.clearTimeout(timeoutRef.current);\n            }\n            setShowNotification(false);\n        }\n        return ()=>{\n            if (timeoutRef.current) {\n                window.clearTimeout(timeoutRef.current);\n            }\n        };\n    }, [\n        props.state\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: showNotification ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed text-sm left-1/2 max-w-[90vw] -translate-x-1/2 flex top-6 items-center gap-4 bg-[#1F1F1F] px-4 py-3 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"24\",\n                        height: \"24\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"none\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            clipRule: \"evenodd\",\n                            d: \"M9.85068 3.63564C10.8197 2.00589 13.1793 2.00589 14.1484 3.63564L21.6323 16.2223C22.6232 17.8888 21.4223 20 19.4835 20H4.51555C2.57676 20 1.37584 17.8888 2.36671 16.2223L9.85068 3.63564ZM12 8.5C12.2761 8.5 12.5 8.72386 12.5 9V13.5C12.5 13.7761 12.2761 14 12 14C11.7239 14 11.5 13.7761 11.5 13.5V9C11.5 8.72386 11.7239 8.5 12 8.5ZM12.75 16C12.75 16.4142 12.4142 16.75 12 16.75C11.5858 16.75 11.25 16.4142 11.25 16C11.25 15.5858 11.5858 15.25 12 15.25C12.4142 15.25 12.75 15.5858 12.75 16Z\",\n                            fill: \"#666666\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-pretty w-max\",\n                    children: \"It's quiet... too quiet. Is your agent lost? Ensure your agent is properly configured and running on your machine.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"https://docs.livekit.io/agents/quickstarts/s2s/\",\n                    target: \"_blank\",\n                    className: \"underline whitespace-nowrap\",\n                    children: \"View guide\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowNotification(false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        width: \"16\",\n                        height: \"16\",\n                        viewBox: \"0 0 16 16\",\n                        fill: \"none\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M3.16602 3.16666L12.8327 12.8333M12.8327 3.16666L3.16602 12.8333\",\n                            stroke: \"#999999\",\n                            strokeWidth: \"1.5\",\n                            strokeLinecap: \"square\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\NoAgentNotification.tsx\",\n            lineNumber: 49,\n            columnNumber: 9\n        }, this) : null\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/NoAgentNotification.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Quiz.tsx":
/*!*****************************!*\
  !*** ./components/Quiz.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Quiz)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Quiz({ question, selectedAnswerId, onAnswerSelect }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full bg-white text-black p-6 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: question.text\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\Quiz.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: question.answers.map((answer)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"radio\",\n                                id: `answer-${answer.id}`,\n                                name: `question-${question.id}`,\n                                value: answer.id,\n                                checked: selectedAnswerId === answer.id,\n                                onChange: ()=>onAnswerSelect(answer.id),\n                                className: \"mr-3 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\Quiz.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: `answer-${answer.id}`,\n                                className: \"flex-1 cursor-pointer\",\n                                children: answer.text\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\Quiz.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, answer.id, true, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\Quiz.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\Quiz.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\Quiz.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Quiz.tsx\n");

/***/ }),

/***/ "(ssr)/./components/QuizContainer.tsx":
/*!**************************************!*\
  !*** ./components/QuizContainer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuizContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/contexts-CPsnPrz2.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-CWooKGw2.mjs\");\n/* harmony import */ var _Quiz__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Quiz */ \"(ssr)/./components/Quiz.tsx\");\n\n\n\n\n\nfunction QuizContainer() {\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [quizId, setQuizId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAnswers, setSelectedAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const room = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_3__.f)();\n    const { agent } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_4__.V)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!room) return;\n        // Register RPC method to receive quizzes\n        const handleShowQuiz = async (data)=>{\n            try {\n                console.log(\"Received quiz RPC data:\", data);\n                // Check for the correct property in the RPC data\n                if (!data || data.payload === undefined) {\n                    console.error(\"Invalid RPC data received:\", data);\n                    return \"Error: Invalid RPC data format\";\n                }\n                console.log(\"Parsing payload:\", data.payload);\n                // Parse the payload string into a JSON object\n                const payload = typeof data.payload === \"string\" ? JSON.parse(data.payload) : data.payload;\n                if (payload.action === \"show\") {\n                    // Reset answers when showing a new quiz\n                    setSelectedAnswers({});\n                    setQuizId(payload.id);\n                    setQuestions(payload.questions);\n                    setCurrentQuestionIndex(0);\n                    setIsVisible(true);\n                } else if (payload.action === \"hide\") {\n                    setIsVisible(false);\n                }\n                return \"Success\";\n            } catch (error) {\n                console.error(\"Error processing quiz data:\", error);\n                return \"Error: \" + (error instanceof Error ? error.message : String(error));\n            }\n        };\n        room.localParticipant.registerRpcMethod(\"client.quiz\", handleShowQuiz);\n        return ()=>{\n            // Clean up RPC method when component unmounts\n            room.localParticipant.unregisterRpcMethod(\"client.quiz\");\n        };\n    }, [\n        room\n    ]);\n    const handleAnswerSelect = (questionId, answerId)=>{\n        setSelectedAnswers((prev)=>({\n                ...prev,\n                [questionId]: answerId\n            }));\n    };\n    const handleSubmitQuiz = async ()=>{\n        if (!agent || !quizId) return;\n        try {\n            console.log(`Submitting quiz ${quizId} to agent ${agent.identity}`);\n            const payload = {\n                id: quizId,\n                answers: selectedAnswers\n            };\n            const result = await room.localParticipant.performRpc({\n                destinationIdentity: agent.identity,\n                method: \"agent.submitQuiz\",\n                payload: JSON.stringify(payload)\n            });\n            console.log(`Quiz submission result: ${result}`);\n            // Hide the quiz after submission\n            setIsVisible(false);\n        } catch (error) {\n            console.error(\"Error submitting quiz:\", error);\n            if (error instanceof Error) {\n                console.error(error.stack);\n            }\n        }\n    };\n    const currentQuestion = currentQuestionIndex !== null && questions[currentQuestionIndex] ? questions[currentQuestionIndex] : null;\n    const isLastQuestion = currentQuestionIndex === questions.length - 1;\n    const allQuestionsAnswered = questions.length > 0 && questions.every((q)=>selectedAnswers[q.id] !== undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n        children: isVisible && currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n            initial: {\n                opacity: 0,\n                x: -100\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            },\n            exit: {\n                opacity: 0,\n                x: -100\n            },\n            className: \"fixed left-8 top-1/4 w-80 bg-gray-900 p-4 rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold\",\n                            children: \"Quiz\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsVisible(false),\n                            className: \"text-gray-400 hover:text-white\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Quiz__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    question: currentQuestion,\n                    selectedAnswerId: selectedAnswers[currentQuestion.id],\n                    onAnswerSelect: (answerId)=>handleAnswerSelect(currentQuestion.id, answerId)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between mt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentQuestionIndex((prev)=>prev !== null ? Math.max(0, prev - 1) : 0),\n                            disabled: currentQuestionIndex === 0,\n                            className: \"px-3 py-1 bg-blue-600 rounded disabled:opacity-50\",\n                            children: \"Previous\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                (currentQuestionIndex ?? 0) + 1,\n                                \" / \",\n                                questions.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this),\n                        !isLastQuestion ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setCurrentQuestionIndex((prev)=>prev !== null ? Math.min(questions.length - 1, prev + 1) : 0),\n                            className: \"px-3 py-1 bg-blue-600 rounded\",\n                            children: \"Next\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSubmitQuiz,\n                            disabled: !allQuestionsAnswered,\n                            className: \"px-3 py-1 bg-green-600 rounded disabled:opacity-50\",\n                            children: \"Submit Quiz\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n            lineNumber: 118,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\QuizContainer.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/QuizContainer.tsx\n");

/***/ }),

/***/ "(ssr)/./components/TranscriptionView.tsx":
/*!******************************************!*\
  !*** ./components/TranscriptionView.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TranscriptionView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useCombinedTranscriptions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useCombinedTranscriptions */ \"(ssr)/./hooks/useCombinedTranscriptions.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction TranscriptionView() {\n    const combinedTranscriptions = (0,_hooks_useCombinedTranscriptions__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const containerRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    // scroll to bottom when new transcription is added\n    react__WEBPACK_IMPORTED_MODULE_2__.useEffect(()=>{\n        if (containerRef.current) {\n            containerRef.current.scrollTop = containerRef.current.scrollHeight;\n        }\n    }, [\n        combinedTranscriptions\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative h-[200px] w-[512px] max-w-[90vw] mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 right-0 h-8 bg-gradient-to-b from-[var(--lk-bg)] to-transparent z-10 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\TranscriptionView.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-[var(--lk-bg)] to-transparent z-10 pointer-events-none\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\TranscriptionView.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: containerRef,\n                className: \"h-full flex flex-col gap-2 overflow-y-auto px-4 py-8\",\n                children: combinedTranscriptions.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: segment.id,\n                        className: segment.role === \"assistant\" ? \"p-2 self-start fit-content\" : \"bg-gray-800 rounded-md p-2 self-end fit-content\",\n                        children: segment.text\n                    }, segment.id, false, {\n                        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\TranscriptionView.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\TranscriptionView.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\components\\\\TranscriptionView.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL1RyYW5zY3JpcHRpb25WaWV3LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTBFO0FBQzNDO0FBRWhCLFNBQVNFO0lBQ3RCLE1BQU1DLHlCQUF5QkgsNEVBQXlCQTtJQUN4RCxNQUFNSSxlQUFlSCx5Q0FBWSxDQUFpQjtJQUVsRCxtREFBbUQ7SUFDbkRBLDRDQUFlLENBQUM7UUFDZCxJQUFJRyxhQUFhRyxPQUFPLEVBQUU7WUFDeEJILGFBQWFHLE9BQU8sQ0FBQ0MsU0FBUyxHQUFHSixhQUFhRyxPQUFPLENBQUNFLFlBQVk7UUFDcEU7SUFDRixHQUFHO1FBQUNOO0tBQXVCO0lBRTNCLHFCQUNFLDhEQUFDTztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7OzswQkFDZiw4REFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7OzswQkFHZiw4REFBQ0Q7Z0JBQUlFLEtBQUtSO2dCQUFjTyxXQUFVOzBCQUMvQlIsdUJBQXVCVSxHQUFHLENBQUMsQ0FBQ0Msd0JBQzNCLDhEQUFDSjt3QkFDQ0ssSUFBSUQsUUFBUUMsRUFBRTt3QkFFZEosV0FDRUcsUUFBUUUsSUFBSSxLQUFLLGNBQ2IsK0JBQ0E7a0NBR0xGLFFBQVFHLElBQUk7dUJBUFJILFFBQVFDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7QUFhM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vY29tcG9uZW50cy9UcmFuc2NyaXB0aW9uVmlldy50c3g/ZTdkYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdXNlQ29tYmluZWRUcmFuc2NyaXB0aW9ucyBmcm9tIFwiQC9ob29rcy91c2VDb21iaW5lZFRyYW5zY3JpcHRpb25zXCI7XHJcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVHJhbnNjcmlwdGlvblZpZXcoKSB7XHJcbiAgY29uc3QgY29tYmluZWRUcmFuc2NyaXB0aW9ucyA9IHVzZUNvbWJpbmVkVHJhbnNjcmlwdGlvbnMoKTtcclxuICBjb25zdCBjb250YWluZXJSZWYgPSBSZWFjdC51c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xyXG5cclxuICAvLyBzY3JvbGwgdG8gYm90dG9tIHdoZW4gbmV3IHRyYW5zY3JpcHRpb24gaXMgYWRkZWRcclxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGNvbnRhaW5lclJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNvbnRhaW5lclJlZi5jdXJyZW50LnNjcm9sbFRvcCA9IGNvbnRhaW5lclJlZi5jdXJyZW50LnNjcm9sbEhlaWdodDtcclxuICAgIH1cclxuICB9LCBbY29tYmluZWRUcmFuc2NyaXB0aW9uc10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLVsyMDBweF0gdy1bNTEycHhdIG1heC13LVs5MHZ3XSBteC1hdXRvXCI+XHJcbiAgICAgIHsvKiBGYWRlLW91dCBncmFkaWVudCBtYXNrICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCByaWdodC0wIGgtOCBiZy1ncmFkaWVudC10by1iIGZyb20tW3ZhcigtLWxrLWJnKV0gdG8tdHJhbnNwYXJlbnQgei0xMCBwb2ludGVyLWV2ZW50cy1ub25lXCIgLz5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tMCBsZWZ0LTAgcmlnaHQtMCBoLTggYmctZ3JhZGllbnQtdG8tdCBmcm9tLVt2YXIoLS1say1iZyldIHRvLXRyYW5zcGFyZW50IHotMTAgcG9pbnRlci1ldmVudHMtbm9uZVwiIC8+XHJcblxyXG4gICAgICB7LyogU2Nyb2xsYWJsZSBjb250ZW50ICovfVxyXG4gICAgICA8ZGl2IHJlZj17Y29udGFpbmVyUmVmfSBjbGFzc05hbWU9XCJoLWZ1bGwgZmxleCBmbGV4LWNvbCBnYXAtMiBvdmVyZmxvdy15LWF1dG8gcHgtNCBweS04XCI+XHJcbiAgICAgICAge2NvbWJpbmVkVHJhbnNjcmlwdGlvbnMubWFwKChzZWdtZW50KSA9PiAoXHJcbiAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgIGlkPXtzZWdtZW50LmlkfVxyXG4gICAgICAgICAgICBrZXk9e3NlZ21lbnQuaWR9XHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17XHJcbiAgICAgICAgICAgICAgc2VnbWVudC5yb2xlID09PSBcImFzc2lzdGFudFwiXHJcbiAgICAgICAgICAgICAgICA/IFwicC0yIHNlbGYtc3RhcnQgZml0LWNvbnRlbnRcIlxyXG4gICAgICAgICAgICAgICAgOiBcImJnLWdyYXktODAwIHJvdW5kZWQtbWQgcC0yIHNlbGYtZW5kIGZpdC1jb250ZW50XCJcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgPlxyXG4gICAgICAgICAgICB7c2VnbWVudC50ZXh0fVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKSl9XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlQ29tYmluZWRUcmFuc2NyaXB0aW9ucyIsIlJlYWN0IiwiVHJhbnNjcmlwdGlvblZpZXciLCJjb21iaW5lZFRyYW5zY3JpcHRpb25zIiwiY29udGFpbmVyUmVmIiwidXNlUmVmIiwidXNlRWZmZWN0IiwiY3VycmVudCIsInNjcm9sbFRvcCIsInNjcm9sbEhlaWdodCIsImRpdiIsImNsYXNzTmFtZSIsInJlZiIsIm1hcCIsInNlZ21lbnQiLCJpZCIsInJvbGUiLCJ0ZXh0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/TranscriptionView.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useCombinedTranscriptions.ts":
/*!********************************************!*\
  !*** ./hooks/useCombinedTranscriptions.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useCombinedTranscriptions)\n/* harmony export */ });\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-CWooKGw2.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useLocalMicTrack__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useLocalMicTrack */ \"(ssr)/./hooks/useLocalMicTrack.ts\");\n\n\n\nfunction useCombinedTranscriptions() {\n    const { agentTranscriptions } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_2__.V)();\n    const micTrackRef = (0,_useLocalMicTrack__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const { segments: userTranscriptions } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_2__.U)(micTrackRef);\n    const combinedTranscriptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        return [\n            ...agentTranscriptions.map((val)=>{\n                return {\n                    ...val,\n                    role: \"assistant\"\n                };\n            }),\n            ...userTranscriptions.map((val)=>{\n                return {\n                    ...val,\n                    role: \"user\"\n                };\n            })\n        ].sort((a, b)=>a.firstReceivedTime - b.firstReceivedTime);\n    }, [\n        agentTranscriptions,\n        userTranscriptions\n    ]);\n    return combinedTranscriptions;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useCombinedTranscriptions.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useLocalMicTrack.ts":
/*!***********************************!*\
  !*** ./hooks/useLocalMicTrack.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLocalMicTrack)\n/* harmony export */ });\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-CWooKGw2.mjs\");\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! livekit-client */ \"(ssr)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction useLocalMicTrack() {\n    const { microphoneTrack, localParticipant } = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_2__.C)();\n    const micTrackRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return {\n            participant: localParticipant,\n            source: livekit_client__WEBPACK_IMPORTED_MODULE_0__.Track.Source.Microphone,\n            publication: microphoneTrack\n        };\n    }, [\n        localParticipant,\n        microphoneTrack\n    ]);\n    return micTrackRef;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ob29rcy91c2VMb2NhbE1pY1RyYWNrLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTZGO0FBQ3REO0FBQ1A7QUFFakIsU0FBU0c7SUFDdEIsTUFBTSxFQUFFQyxlQUFlLEVBQUVDLGdCQUFnQixFQUFFLEdBQUdMLDREQUFtQkE7SUFFakUsTUFBTU0sY0FBMkNKLDhDQUFPQSxDQUFDO1FBQ3ZELE9BQU87WUFDTEssYUFBYUY7WUFDYkcsUUFBUVAsaURBQUtBLENBQUNRLE1BQU0sQ0FBQ0MsVUFBVTtZQUMvQkMsYUFBYVA7UUFDZjtJQUNGLEdBQUc7UUFBQ0M7UUFBa0JEO0tBQWdCO0lBRXRDLE9BQU9FO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vaG9va3MvdXNlTG9jYWxNaWNUcmFjay50cz9kZTZlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRyYWNrUmVmZXJlbmNlT3JQbGFjZWhvbGRlciwgdXNlTG9jYWxQYXJ0aWNpcGFudCB9IGZyb20gXCJAbGl2ZWtpdC9jb21wb25lbnRzLXJlYWN0XCI7XHJcbmltcG9ydCB7IFRyYWNrIH0gZnJvbSBcImxpdmVraXQtY2xpZW50XCI7XHJcbmltcG9ydCB7IHVzZU1lbW8gfSBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZUxvY2FsTWljVHJhY2soKSB7XHJcbiAgY29uc3QgeyBtaWNyb3Bob25lVHJhY2ssIGxvY2FsUGFydGljaXBhbnQgfSA9IHVzZUxvY2FsUGFydGljaXBhbnQoKTtcclxuXHJcbiAgY29uc3QgbWljVHJhY2tSZWY6IFRyYWNrUmVmZXJlbmNlT3JQbGFjZWhvbGRlciA9IHVzZU1lbW8oKCkgPT4ge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgcGFydGljaXBhbnQ6IGxvY2FsUGFydGljaXBhbnQsXHJcbiAgICAgIHNvdXJjZTogVHJhY2suU291cmNlLk1pY3JvcGhvbmUsXHJcbiAgICAgIHB1YmxpY2F0aW9uOiBtaWNyb3Bob25lVHJhY2ssXHJcbiAgICB9O1xyXG4gIH0sIFtsb2NhbFBhcnRpY2lwYW50LCBtaWNyb3Bob25lVHJhY2tdKTtcclxuXHJcbiAgcmV0dXJuIG1pY1RyYWNrUmVmO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VMb2NhbFBhcnRpY2lwYW50IiwiVHJhY2siLCJ1c2VNZW1vIiwidXNlTG9jYWxNaWNUcmFjayIsIm1pY3JvcGhvbmVUcmFjayIsImxvY2FsUGFydGljaXBhbnQiLCJtaWNUcmFja1JlZiIsInBhcnRpY2lwYW50Iiwic291cmNlIiwiU291cmNlIiwiTWljcm9waG9uZSIsInB1YmxpY2F0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useLocalMicTrack.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6e5485df5e8f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vYXBwL2dsb2JhbHMuY3NzPzQ1OWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2ZTU0ODVkZjVlOGZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Public_Sans_arguments_weight_400_subsets_latin_variableName_publicSans400___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Public_Sans\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"]}],\"variableName\":\"publicSans400\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Public_Sans\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"400\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"publicSans400\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Public_Sans_arguments_weight_400_subsets_latin_variableName_publicSans400___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Public_Sans_arguments_weight_400_subsets_latin_variableName_publicSans400___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _livekit_components_styles__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @livekit/components-styles */ \"(rsc)/./node_modules/@livekit/components-styles/dist/general/index.css\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Voice Assistant\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `h-full ${(next_font_google_target_css_path_app_layout_tsx_import_Public_Sans_arguments_weight_400_subsets_latin_variableName_publicSans400___WEBPACK_IMPORTED_MODULE_3___default().className)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"h-full\",\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFLTUE7QUFMOEI7QUFHYjtBQU9oQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztBQUNULEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFXLENBQUMsT0FBTyxFQUFFUCxvTEFBdUIsQ0FBQyxDQUFDO2tCQUM1RCw0RUFBQ1E7WUFBS0QsV0FBVTtzQkFBVUg7Ozs7Ozs7Ozs7O0FBR2hDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdm9pY2UtYXNzaXN0YW50Mi8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiQGxpdmVraXQvY29tcG9uZW50cy1zdHlsZXNcIjtcclxuaW1wb3J0IHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5pbXBvcnQgeyBQdWJsaWNfU2FucyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcclxuXHJcbmNvbnN0IHB1YmxpY1NhbnM0MDAgPSBQdWJsaWNfU2Fucyh7XHJcbiAgd2VpZ2h0OiBcIjQwMFwiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6IFwiVm9pY2UgQXNzaXN0YW50XCIsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufTogUmVhZG9ubHk8e1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn0+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT17YGgtZnVsbCAke3B1YmxpY1NhbnM0MDAuY2xhc3NOYW1lfWB9PlxyXG4gICAgICA8Ym9keSBjbGFzc05hbWU9XCJoLWZ1bGxcIj57Y2hpbGRyZW59PC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInB1YmxpY1NhbnM0MDAiLCJtZXRhZGF0YSIsInRpdGxlIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Work\Projects\2025\livekitdemo\python-agents-examples\avatars\tavus\voice-assistant-frontend\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL3ZvaWNlLWFzc2lzdGFudDIvLi9hcHAvZmF2aWNvbi5pY28/Y2RjYSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@livekit","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/livekit-client"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();