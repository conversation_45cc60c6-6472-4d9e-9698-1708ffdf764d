"use client";

import { CloseIcon } from "@/components/CloseIcon";
import { NoAgentNotification } from "@/components/NoAgentNotification";
import ChatView from "@/components/ChatView";
import { ChatIcon, MicDisabledIcon } from "@livekit/components-react";
import {
  BarVisualizer,
  DisconnectButton,
  RoomAudioRenderer,
  RoomContext,
  VideoTrack,
  VoiceAssistantControlBar,
  useRoomContext,
  useVoiceAssistant,
} from "@livekit/components-react";
import { AnimatePresence, motion } from "motion/react";
import { Room, RoomEvent, type RpcInvocationData } from "livekit-client";
import { useCallback, useEffect, useState } from "react";
import type { ConnectionDetails } from "./api/connection-details/route";
import React from "react";
import { AudioDisabledIcon } from "@/components/AudioDisabledIcon";

export default function Page() {
  const [room] = useState(new Room());

  const onConnectButtonClicked = useCallback(async () => {
    // Generate room connection details, including:
    //   - A random Room name
    //   - A random Participant name
    //   - An Access Token to permit the participant to join the room
    //   - The URL of the LiveKit server to connect to
    //
    // In real-world application, you would likely allow the user to specify their
    // own participant name, and possibly to choose from existing rooms to join.

    const url = new URL(
      process.env.NEXT_PUBLIC_CONN_DETAILS_ENDPOINT ?? "/api/connection-details",
      window.location.origin
    );
    const response = await fetch(url.toString());
    const connectionDetailsData: ConnectionDetails = await response.json();

    await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);
    await room.localParticipant.setMicrophoneEnabled(true);
  }, [room]);

  useEffect(() => {
    room.on(RoomEvent.MediaDevicesError, onDeviceFailure);

    return () => {
      room.off(RoomEvent.MediaDevicesError, onDeviceFailure);
    };
  }, [room]);

  return (
    <main data-lk-theme="default" className="flex h-full bg-[var(--lk-bg)]">
      <RoomContext.Provider value={room}>
        <div className={"flex flex-col grow w-full"}>
          <SimpleVoiceAssistant onConnectButtonClicked={onConnectButtonClicked} room={room} />
        </div>
      </RoomContext.Provider>
    </main>
  );
}

function SimpleVoiceAssistant(props: { onConnectButtonClicked: () => void, room: Room }) {
  const { state: agentState, agent } = useVoiceAssistant();
  const [showChat, setShowChat] = useState(false);

  async function toggleChat(): Promise<void> {
    setShowChat(!showChat);

    if (!agent) {
      console.error("Agent not found");
      return;
    }

    const payload = {
      audio: showChat,
    };

    const result = await props.room.localParticipant.performRpc({
      destinationIdentity: agent.identity,
      method: "agent.updateSettings",
      payload: JSON.stringify(payload)
    });
    
    console.log(`Update settings result: ${result}`);
  }

  return (
    <>
      <AnimatePresence mode="wait">
        {agentState === "disconnected" ? (
          <motion.div
            key="disconnected"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            className="grid items-center justify-center h-full"
          >
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="uppercase px-4 py-2 bg-white text-black rounded-md"
              onClick={() => props.onConnectButtonClicked()}
            >
              Начать презентацию
            </motion.button>
          </motion.div>
        ) : (
          <>
            <motion.div
              key="connected"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
              className="flex flex-row gap-4"
              style={{height: "calc(100% - 80px)"}}
            >
              <div className="flex flex-col w-full">
                <div className="w-full h-full">
                  <PresentationView showChat={showChat} />
                </div>
                <RoomAudioRenderer />
                <NoAgentNotification state={agentState} />
              </div>
              
              <div className={`flex flex-col w-3/12 ${showChat ? "" : "hidden"}`}>
                <ChatView />
              </div>
              
            </motion.div>
            <div className="flex w-full justify-center">
              <ControlBar onConnectButtonClicked={props.onConnectButtonClicked} toggleChat={toggleChat} chatOpened={showChat} />
            </div>
          </>
          
        )}
      </AnimatePresence>
    </>
  );
}

function PresentationView(props: { showChat: boolean }) {
  const { state: agentState, videoTrack, audioTrack } = useVoiceAssistant();
  const [slideUrl, setSlideUrl] = useState<string | null>(null);
  const room = useRoomContext();

  useEffect(() => {
    if (!room) return;

    room.localParticipant.registerRpcMethod(
      "client.switchslide",
      async (data: RpcInvocationData) => {
        const payload = JSON.parse(data.payload);
        setSlideUrl(payload.url);

        console.log("Received switch slide RPC data:", data);
        
        return "Success";
      }
    );

    return () => {
      // Clean up RPC method when component unmounts
      room.localParticipant.unregisterRpcMethod("client.switchslide");
    };
  }, [room]);

  return (
    <AnimatePresence>
      <div className={`h-full w-full ${ (props.showChat && !slideUrl) && "flex items-center justify-center"}`}>
          {slideUrl ? (
            <>
              <motion.div
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 100 }}
                className="relative flex h-full flex-col bg-gray-900 p-4 rounded-lg"
              >
                <div className="flex h-full bg-transparent bg-no-repeat bg-contain bg-center" style={{backgroundImage: `url(${slideUrl})`}}>
                  &nbsp;
                </div>

                <div className="absolute bottom-0 right-0 w-[150px] h-[150px] m-2">
                  {props.showChat ? (
                    <div className="flex rounded-full border-4 border-white px-4 w-[150px] h-[150px] items-center justify-center">
                      <AudioDisabledIcon />
                    </div>
                  ) : (
                    <>
                      {videoTrack ? (
                        <VideoTrack 
                          trackRef={videoTrack} 
                          className="rounded-full border-4 border-white px-4"
                        />
                      ) : (
                        <BarVisualizer
                          state={agentState}
                          barCount={4}
                          trackRef={audioTrack}
                          className="agent-visualizer rounded-full border-4 border-white px-4"
                          options={{ minHeight: 24 }}
                        />
                      )}
                    </>
                  )}
                </div>
              </motion.div>
            </>
          ) : (
            <>
              {props.showChat ? (
                <div className="flex rounded-full border-4 border-white px-4 w-[150px] h-[150px] items-center justify-center">
                  <AudioDisabledIcon />
                </div>
              ) : (
                <>
                  {videoTrack ? (
                    <VideoTrack trackRef={videoTrack} />
                  ) : (
                    <BarVisualizer
                      state={agentState}
                      barCount={5}
                      trackRef={audioTrack}
                      className="agent-visualizer"
                      options={{ minHeight: 24 }}
                    />
                  )}
                </>
              )}
            </>
        )}
      
    </div>
    </AnimatePresence>
  );
}

function ControlBar(props: { onConnectButtonClicked: () => void, toggleChat: () => Promise<void>, chatOpened: boolean }) {
  const { state: agentState } = useVoiceAssistant();

  return (
    <div className="flex h-[60px] items-center">
      <AnimatePresence>
        {agentState === "disconnected" ? (
          <motion.button
            initial={{ opacity: 0, top: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0, top: "-10px" }}
            transition={{ duration: 1, ease: [0.09, 1.04, 0.245, 1.055] }}
            className="uppercase absolute left-1/2 -translate-x-1/2 px-4 py-2 bg-white text-black rounded-md"
            onClick={() => props.onConnectButtonClicked()}
          >
            Начать презентацию
          </motion.button>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4, ease: [0.09, 1.04, 0.245, 1.055] }}
            className="flex h-8"
          >
            <VoiceAssistantControlBar controls={{ leave: false, microphone: !props.chatOpened }} />
            <DisconnectButton>
              <CloseIcon />
            </DisconnectButton>
            <button 
              type="button"
              onClick={async () => props.toggleChat()}
              className={`flex items-center justify-center w-[50px] h-[36px] px-3 py-1 ml-2 text-xs rounded-md cursor-pointer transition-all duration-200 border-none outline-none  ${props.chatOpened 
                ? 'bg-gray-700 text-amber-500 hover:bg-gray-800 hover:text-white' 
                : 'bg-gray-800 hover:bg-gray-700 hover:text-amber-500'}`}
              aria-label={props.chatOpened ? "Закрыть чат" : "Открыть чат"}
            >
              <ChatIcon />
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

function onDeviceFailure(error: Error) {
  console.error(error);
  alert(
    "Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab"
  );
}
