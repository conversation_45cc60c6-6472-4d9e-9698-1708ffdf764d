# Tavus Presentation System

AI-powered presentation system with Tavus avatar integration for Infinity Automate company.

## Project Structure

- `tavus_presentation_back/` - Backend Python application with Tavus avatar agent
- `tavus_presenntation_frontend/` - Frontend Next.js application for presentation interface

## Features

- Interactive AI avatar powered by Tavus
- Voice-controlled presentation navigation
- Real-time presentation slides display
- AI agent representing Infinity Automate company
- Support for various AI automation solutions presentation

## Backend (tavus_presentation_back)

### Requirements

- Python 3.10+
- LiveKit account
- Tavus account with configured avatar
- API keys for OpenAI, ElevenLabs, Deepgram, Tavus

### Setup

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure environment variables in `.env` file:
   ```
   OPENAI_API_KEY=your_openai_key
   ELEVENLABS_API_KEY=your_elevenlabs_key
   DEEPGRAM_API_KEY=your_deepgram_key
   TAVUS_API_KEY=your_tavus_key
   LIVEKIT_API_KEY=your_livekit_key
   LIVEKIT_API_SECRET=your_livekit_secret
   LIVEKIT_URL=your_livekit_url
   ```

3. Run the agent:
   ```bash
   python tavus_presentation.py dev
   ```

## Frontend (tavus_presenntation_frontend)

### Requirements

- Node.js 18+
- npm or pnpm

### Setup

1. Install dependencies:
   ```bash
   cd tavus_presenntation_frontend
   npm install
   ```

2. Configure environment variables in `.env.local`:
   ```
   LIVEKIT_URL=your_livekit_url
   LIVEKIT_API_KEY=your_livekit_key
   LIVEKIT_API_SECRET=your_livekit_secret
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. Start the backend agent
2. Start the frontend application
3. Open the web interface and connect to the agent
4. Interact with the AI avatar to navigate through the presentation
5. Ask questions about Infinity Automate's AI solutions

## AI Agent Capabilities

The AI agent can:
- Present information about Infinity Automate's AI solutions
- Navigate through presentation slides
- Answer questions about different AI agents (Finance, HR, Logistics, etc.)
- Create interactive flash cards and quizzes
- Provide detailed explanations of automation benefits

## Company Information

Infinity Automate specializes in building smart AI solutions for businesses across various domains:
- Accounting and Finance
- Human Resources
- Logistics and Supply Chain
- Marketing and Sales
- Project Management
- Legal and Compliance
- And more...

## License

This project is licensed under the MIT License.
