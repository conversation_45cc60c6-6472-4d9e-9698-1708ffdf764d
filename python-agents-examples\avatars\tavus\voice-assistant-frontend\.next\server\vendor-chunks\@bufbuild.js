"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@bufbuild";
exports.ids = ["vendor-chunks/@bufbuild"];
exports.modules = {

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BinaryReader: () => (/* binding */ BinaryReader),\n/* harmony export */   BinaryWriter: () => (/* binding */ BinaryWriter),\n/* harmony export */   WireType: () => (/* binding */ WireType)\n/* harmony export */ });\n/* harmony import */ var _google_varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./google/varint.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js\");\n/* harmony import */ var _private_assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./proto-int64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/restrict-plus-operands */\n/**\n * Protobuf binary format wire types.\n *\n * A wire type provides just enough information to find the length of the\n * following value.\n *\n * See https://developers.google.com/protocol-buffers/docs/encoding#structure\n */\nvar WireType;\n(function (WireType) {\n    /**\n     * Used for int32, int64, uint32, uint64, sint32, sint64, bool, enum\n     */\n    WireType[WireType[\"Varint\"] = 0] = \"Varint\";\n    /**\n     * Used for fixed64, sfixed64, double.\n     * Always 8 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit64\"] = 1] = \"Bit64\";\n    /**\n     * Used for string, bytes, embedded messages, packed repeated fields\n     *\n     * Only repeated numeric types (types which use the varint, 32-bit,\n     * or 64-bit wire types) can be packed. In proto3, such fields are\n     * packed by default.\n     */\n    WireType[WireType[\"LengthDelimited\"] = 2] = \"LengthDelimited\";\n    /**\n     * Start of a tag-delimited aggregate, such as a proto2 group, or a message\n     * in editions with message_encoding = DELIMITED.\n     */\n    WireType[WireType[\"StartGroup\"] = 3] = \"StartGroup\";\n    /**\n     * End of a tag-delimited aggregate.\n     */\n    WireType[WireType[\"EndGroup\"] = 4] = \"EndGroup\";\n    /**\n     * Used for fixed32, sfixed32, float.\n     * Always 4 bytes with little-endian byte order.\n     */\n    WireType[WireType[\"Bit32\"] = 5] = \"Bit32\";\n})(WireType || (WireType = {}));\nclass BinaryWriter {\n    constructor(textEncoder) {\n        /**\n         * Previous fork states.\n         */\n        this.stack = [];\n        this.textEncoder = textEncoder !== null && textEncoder !== void 0 ? textEncoder : new TextEncoder();\n        this.chunks = [];\n        this.buf = [];\n    }\n    /**\n     * Return all bytes written and reset this writer.\n     */\n    finish() {\n        this.chunks.push(new Uint8Array(this.buf)); // flush the buffer\n        let len = 0;\n        for (let i = 0; i < this.chunks.length; i++)\n            len += this.chunks[i].length;\n        let bytes = new Uint8Array(len);\n        let offset = 0;\n        for (let i = 0; i < this.chunks.length; i++) {\n            bytes.set(this.chunks[i], offset);\n            offset += this.chunks[i].length;\n        }\n        this.chunks = [];\n        return bytes;\n    }\n    /**\n     * Start a new fork for length-delimited data like a message\n     * or a packed repeated field.\n     *\n     * Must be joined later with `join()`.\n     */\n    fork() {\n        this.stack.push({ chunks: this.chunks, buf: this.buf });\n        this.chunks = [];\n        this.buf = [];\n        return this;\n    }\n    /**\n     * Join the last fork. Write its length and bytes, then\n     * return to the previous state.\n     */\n    join() {\n        // get chunk of fork\n        let chunk = this.finish();\n        // restore previous state\n        let prev = this.stack.pop();\n        if (!prev)\n            throw new Error(\"invalid state, fork stack empty\");\n        this.chunks = prev.chunks;\n        this.buf = prev.buf;\n        // write length of chunk as varint\n        this.uint32(chunk.byteLength);\n        return this.raw(chunk);\n    }\n    /**\n     * Writes a tag (field number and wire type).\n     *\n     * Equivalent to `uint32( (fieldNo << 3 | type) >>> 0 )`.\n     *\n     * Generated code should compute the tag ahead of time and call `uint32()`.\n     */\n    tag(fieldNo, type) {\n        return this.uint32(((fieldNo << 3) | type) >>> 0);\n    }\n    /**\n     * Write a chunk of raw bytes.\n     */\n    raw(chunk) {\n        if (this.buf.length) {\n            this.chunks.push(new Uint8Array(this.buf));\n            this.buf = [];\n        }\n        this.chunks.push(chunk);\n        return this;\n    }\n    /**\n     * Write a `uint32` value, an unsigned 32 bit varint.\n     */\n    uint32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertUInt32)(value);\n        // write value as varint 32, inlined for speed\n        while (value > 0x7f) {\n            this.buf.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        this.buf.push(value);\n        return this;\n    }\n    /**\n     * Write a `int32` value, a signed 32 bit varint.\n     */\n    int32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertInt32)(value);\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `bool` value, a variant.\n     */\n    bool(value) {\n        this.buf.push(value ? 1 : 0);\n        return this;\n    }\n    /**\n     * Write a `bytes` value, length-delimited arbitrary data.\n     */\n    bytes(value) {\n        this.uint32(value.byteLength); // write length of chunk as varint\n        return this.raw(value);\n    }\n    /**\n     * Write a `string` value, length-delimited data converted to UTF-8 text.\n     */\n    string(value) {\n        let chunk = this.textEncoder.encode(value);\n        this.uint32(chunk.byteLength); // write length of chunk as varint\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `float` value, 32-bit floating point number.\n     */\n    float(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertFloat32)(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setFloat32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `double` value, a 64-bit floating point number.\n     */\n    double(value) {\n        let chunk = new Uint8Array(8);\n        new DataView(chunk.buffer).setFloat64(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed32` value, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertUInt32)(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setUint32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sfixed32` value, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertInt32)(value);\n        let chunk = new Uint8Array(4);\n        new DataView(chunk.buffer).setInt32(0, value, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `sint32` value, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32(value) {\n        (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assertInt32)(value);\n        // zigzag encode\n        value = ((value << 1) ^ (value >> 31)) >>> 0;\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32write)(value, this.buf);\n        return this;\n    }\n    /**\n     * Write a `fixed64` value, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `fixed64` value, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64(value) {\n        let chunk = new Uint8Array(8), view = new DataView(chunk.buffer), tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        view.setInt32(0, tc.lo, true);\n        view.setInt32(4, tc.hi, true);\n        return this.raw(chunk);\n    }\n    /**\n     * Write a `int64` value, a signed 64-bit varint.\n     */\n    int64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value);\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `sint64` value, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.enc(value), \n        // zigzag encode\n        sign = tc.hi >> 31, lo = (tc.lo << 1) ^ sign, hi = ((tc.hi << 1) | (tc.lo >>> 31)) ^ sign;\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(lo, hi, this.buf);\n        return this;\n    }\n    /**\n     * Write a `uint64` value, an unsigned 64-bit varint.\n     */\n    uint64(value) {\n        let tc = _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uEnc(value);\n        (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64write)(tc.lo, tc.hi, this.buf);\n        return this;\n    }\n}\nclass BinaryReader {\n    constructor(buf, textDecoder) {\n        this.varint64 = _google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint64read; // dirty cast for `this`\n        /**\n         * Read a `uint32` field, an unsigned 32 bit varint.\n         */\n        this.uint32 = _google_varint_js__WEBPACK_IMPORTED_MODULE_1__.varint32read; // dirty cast for `this` and access to protected `buf`\n        this.buf = buf;\n        this.len = buf.length;\n        this.pos = 0;\n        this.view = new DataView(buf.buffer, buf.byteOffset, buf.byteLength);\n        this.textDecoder = textDecoder !== null && textDecoder !== void 0 ? textDecoder : new TextDecoder();\n    }\n    /**\n     * Reads a tag - field number and wire type.\n     */\n    tag() {\n        let tag = this.uint32(), fieldNo = tag >>> 3, wireType = tag & 7;\n        if (fieldNo <= 0 || wireType < 0 || wireType > 5)\n            throw new Error(\"illegal tag: field no \" + fieldNo + \" wire type \" + wireType);\n        return [fieldNo, wireType];\n    }\n    /**\n     * Skip one element and return the skipped data.\n     *\n     * When skipping StartGroup, provide the tags field number to check for\n     * matching field number in the EndGroup tag.\n     */\n    skip(wireType, fieldNo) {\n        let start = this.pos;\n        switch (wireType) {\n            case WireType.Varint:\n                while (this.buf[this.pos++] & 0x80) {\n                    // ignore\n                }\n                break;\n            // eslint-disable-next-line\n            // @ts-ignore TS7029: Fallthrough case in switch\n            case WireType.Bit64:\n                this.pos += 4;\n            // eslint-disable-next-line\n            // @ts-ignore TS7029: Fallthrough case in switch\n            case WireType.Bit32:\n                this.pos += 4;\n                break;\n            case WireType.LengthDelimited:\n                let len = this.uint32();\n                this.pos += len;\n                break;\n            case WireType.StartGroup:\n                for (;;) {\n                    const [fn, wt] = this.tag();\n                    if (wt === WireType.EndGroup) {\n                        if (fieldNo !== undefined && fn !== fieldNo) {\n                            throw new Error(\"invalid end group tag\");\n                        }\n                        break;\n                    }\n                    this.skip(wt, fn);\n                }\n                break;\n            default:\n                throw new Error(\"cant skip wire type \" + wireType);\n        }\n        this.assertBounds();\n        return this.buf.subarray(start, this.pos);\n    }\n    /**\n     * Throws error if position in byte array is out of range.\n     */\n    assertBounds() {\n        if (this.pos > this.len)\n            throw new RangeError(\"premature EOF\");\n    }\n    /**\n     * Read a `int32` field, a signed 32 bit varint.\n     */\n    int32() {\n        return this.uint32() | 0;\n    }\n    /**\n     * Read a `sint32` field, a signed, zigzag-encoded 32-bit varint.\n     */\n    sint32() {\n        let zze = this.uint32();\n        // decode zigzag\n        return (zze >>> 1) ^ -(zze & 1);\n    }\n    /**\n     * Read a `int64` field, a signed 64-bit varint.\n     */\n    int64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(...this.varint64());\n    }\n    /**\n     * Read a `uint64` field, an unsigned 64-bit varint.\n     */\n    uint64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(...this.varint64());\n    }\n    /**\n     * Read a `sint64` field, a signed, zig-zag-encoded 64-bit varint.\n     */\n    sint64() {\n        let [lo, hi] = this.varint64();\n        // decode zig zag\n        let s = -(lo & 1);\n        lo = ((lo >>> 1) | ((hi & 1) << 31)) ^ s;\n        hi = (hi >>> 1) ^ s;\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(lo, hi);\n    }\n    /**\n     * Read a `bool` field, a variant.\n     */\n    bool() {\n        let [lo, hi] = this.varint64();\n        return lo !== 0 || hi !== 0;\n    }\n    /**\n     * Read a `fixed32` field, an unsigned, fixed-length 32-bit integer.\n     */\n    fixed32() {\n        return this.view.getUint32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `sfixed32` field, a signed, fixed-length 32-bit integer.\n     */\n    sfixed32() {\n        return this.view.getInt32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `fixed64` field, an unsigned, fixed-length 64 bit integer.\n     */\n    fixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.uDec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `fixed64` field, a signed, fixed-length 64-bit integer.\n     */\n    sfixed64() {\n        return _proto_int64_js__WEBPACK_IMPORTED_MODULE_2__.protoInt64.dec(this.sfixed32(), this.sfixed32());\n    }\n    /**\n     * Read a `float` field, 32-bit floating point number.\n     */\n    float() {\n        return this.view.getFloat32((this.pos += 4) - 4, true);\n    }\n    /**\n     * Read a `double` field, a 64-bit floating point number.\n     */\n    double() {\n        return this.view.getFloat64((this.pos += 8) - 8, true);\n    }\n    /**\n     * Read a `bytes` field, length-delimited arbitrary data.\n     */\n    bytes() {\n        let len = this.uint32(), start = this.pos;\n        this.pos += len;\n        this.assertBounds();\n        return this.buf.subarray(start, start + len);\n    }\n    /**\n     * Read a `string` field, length-delimited data converted to UTF-8 text.\n     */\n    string() {\n        return this.textDecoder.decode(this.bytes());\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL2JpbmFyeS1lbmNvZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUMrRjtBQUNoQjtBQUNqQztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLDRCQUE0QjtBQUN0QjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9EO0FBQ3BEO0FBQ0Esd0JBQXdCLHdCQUF3QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isd0JBQXdCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLG9DQUFvQztBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsZ0VBQVk7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwrREFBVztBQUNuQixRQUFRLGdFQUFhO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdFQUFZO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLCtEQUFXO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLCtEQUFXO0FBQ25CO0FBQ0E7QUFDQSxRQUFRLGdFQUFhO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtFQUErRSx1REFBVTtBQUN6RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0VBQStFLHVEQUFVO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsdURBQVU7QUFDM0IsUUFBUSxnRUFBYTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsdURBQVU7QUFDM0I7QUFDQTtBQUNBLFFBQVEsZ0VBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHVEQUFVO0FBQzNCLFFBQVEsZ0VBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLHdCQUF3QiwyREFBWSxFQUFFO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwyREFBWSxFQUFFO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHVEQUFVO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHVEQUFVO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSx1REFBVTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHVEQUFVO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHVEQUFVO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vbm9kZV9tb2R1bGVzL0BidWZidWlsZC9wcm90b2J1Zi9kaXN0L2VzbS9iaW5hcnktZW5jb2RpbmcuanM/NDY5YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAyMS0yMDI0IEJ1ZiBUZWNobm9sb2dpZXMsIEluYy5cbi8vXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuaW1wb3J0IHsgdmFyaW50MzJyZWFkLCB2YXJpbnQzMndyaXRlLCB2YXJpbnQ2NHJlYWQsIHZhcmludDY0d3JpdGUsIH0gZnJvbSBcIi4vZ29vZ2xlL3ZhcmludC5qc1wiO1xuaW1wb3J0IHsgYXNzZXJ0RmxvYXQzMiwgYXNzZXJ0SW50MzIsIGFzc2VydFVJbnQzMiB9IGZyb20gXCIuL3ByaXZhdGUvYXNzZXJ0LmpzXCI7XG5pbXBvcnQgeyBwcm90b0ludDY0IH0gZnJvbSBcIi4vcHJvdG8taW50NjQuanNcIjtcbi8qIGVzbGludC1kaXNhYmxlIHByZWZlci1jb25zdCxuby1jYXNlLWRlY2xhcmF0aW9ucyxAdHlwZXNjcmlwdC1lc2xpbnQvcmVzdHJpY3QtcGx1cy1vcGVyYW5kcyAqL1xuLyoqXG4gKiBQcm90b2J1ZiBiaW5hcnkgZm9ybWF0IHdpcmUgdHlwZXMuXG4gKlxuICogQSB3aXJlIHR5cGUgcHJvdmlkZXMganVzdCBlbm91Z2ggaW5mb3JtYXRpb24gdG8gZmluZCB0aGUgbGVuZ3RoIG9mIHRoZVxuICogZm9sbG93aW5nIHZhbHVlLlxuICpcbiAqIFNlZSBodHRwczovL2RldmVsb3BlcnMuZ29vZ2xlLmNvbS9wcm90b2NvbC1idWZmZXJzL2RvY3MvZW5jb2Rpbmcjc3RydWN0dXJlXG4gKi9cbmV4cG9ydCB2YXIgV2lyZVR5cGU7XG4oZnVuY3Rpb24gKFdpcmVUeXBlKSB7XG4gICAgLyoqXG4gICAgICogVXNlZCBmb3IgaW50MzIsIGludDY0LCB1aW50MzIsIHVpbnQ2NCwgc2ludDMyLCBzaW50NjQsIGJvb2wsIGVudW1cbiAgICAgKi9cbiAgICBXaXJlVHlwZVtXaXJlVHlwZVtcIlZhcmludFwiXSA9IDBdID0gXCJWYXJpbnRcIjtcbiAgICAvKipcbiAgICAgKiBVc2VkIGZvciBmaXhlZDY0LCBzZml4ZWQ2NCwgZG91YmxlLlxuICAgICAqIEFsd2F5cyA4IGJ5dGVzIHdpdGggbGl0dGxlLWVuZGlhbiBieXRlIG9yZGVyLlxuICAgICAqL1xuICAgIFdpcmVUeXBlW1dpcmVUeXBlW1wiQml0NjRcIl0gPSAxXSA9IFwiQml0NjRcIjtcbiAgICAvKipcbiAgICAgKiBVc2VkIGZvciBzdHJpbmcsIGJ5dGVzLCBlbWJlZGRlZCBtZXNzYWdlcywgcGFja2VkIHJlcGVhdGVkIGZpZWxkc1xuICAgICAqXG4gICAgICogT25seSByZXBlYXRlZCBudW1lcmljIHR5cGVzICh0eXBlcyB3aGljaCB1c2UgdGhlIHZhcmludCwgMzItYml0LFxuICAgICAqIG9yIDY0LWJpdCB3aXJlIHR5cGVzKSBjYW4gYmUgcGFja2VkLiBJbiBwcm90bzMsIHN1Y2ggZmllbGRzIGFyZVxuICAgICAqIHBhY2tlZCBieSBkZWZhdWx0LlxuICAgICAqL1xuICAgIFdpcmVUeXBlW1dpcmVUeXBlW1wiTGVuZ3RoRGVsaW1pdGVkXCJdID0gMl0gPSBcIkxlbmd0aERlbGltaXRlZFwiO1xuICAgIC8qKlxuICAgICAqIFN0YXJ0IG9mIGEgdGFnLWRlbGltaXRlZCBhZ2dyZWdhdGUsIHN1Y2ggYXMgYSBwcm90bzIgZ3JvdXAsIG9yIGEgbWVzc2FnZVxuICAgICAqIGluIGVkaXRpb25zIHdpdGggbWVzc2FnZV9lbmNvZGluZyA9IERFTElNSVRFRC5cbiAgICAgKi9cbiAgICBXaXJlVHlwZVtXaXJlVHlwZVtcIlN0YXJ0R3JvdXBcIl0gPSAzXSA9IFwiU3RhcnRHcm91cFwiO1xuICAgIC8qKlxuICAgICAqIEVuZCBvZiBhIHRhZy1kZWxpbWl0ZWQgYWdncmVnYXRlLlxuICAgICAqL1xuICAgIFdpcmVUeXBlW1dpcmVUeXBlW1wiRW5kR3JvdXBcIl0gPSA0XSA9IFwiRW5kR3JvdXBcIjtcbiAgICAvKipcbiAgICAgKiBVc2VkIGZvciBmaXhlZDMyLCBzZml4ZWQzMiwgZmxvYXQuXG4gICAgICogQWx3YXlzIDQgYnl0ZXMgd2l0aCBsaXR0bGUtZW5kaWFuIGJ5dGUgb3JkZXIuXG4gICAgICovXG4gICAgV2lyZVR5cGVbV2lyZVR5cGVbXCJCaXQzMlwiXSA9IDVdID0gXCJCaXQzMlwiO1xufSkoV2lyZVR5cGUgfHwgKFdpcmVUeXBlID0ge30pKTtcbmV4cG9ydCBjbGFzcyBCaW5hcnlXcml0ZXIge1xuICAgIGNvbnN0cnVjdG9yKHRleHRFbmNvZGVyKSB7XG4gICAgICAgIC8qKlxuICAgICAgICAgKiBQcmV2aW91cyBmb3JrIHN0YXRlcy5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMuc3RhY2sgPSBbXTtcbiAgICAgICAgdGhpcy50ZXh0RW5jb2RlciA9IHRleHRFbmNvZGVyICE9PSBudWxsICYmIHRleHRFbmNvZGVyICE9PSB2b2lkIDAgPyB0ZXh0RW5jb2RlciA6IG5ldyBUZXh0RW5jb2RlcigpO1xuICAgICAgICB0aGlzLmNodW5rcyA9IFtdO1xuICAgICAgICB0aGlzLmJ1ZiA9IFtdO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXR1cm4gYWxsIGJ5dGVzIHdyaXR0ZW4gYW5kIHJlc2V0IHRoaXMgd3JpdGVyLlxuICAgICAqL1xuICAgIGZpbmlzaCgpIHtcbiAgICAgICAgdGhpcy5jaHVua3MucHVzaChuZXcgVWludDhBcnJheSh0aGlzLmJ1ZikpOyAvLyBmbHVzaCB0aGUgYnVmZmVyXG4gICAgICAgIGxldCBsZW4gPSAwO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuY2h1bmtzLmxlbmd0aDsgaSsrKVxuICAgICAgICAgICAgbGVuICs9IHRoaXMuY2h1bmtzW2ldLmxlbmd0aDtcbiAgICAgICAgbGV0IGJ5dGVzID0gbmV3IFVpbnQ4QXJyYXkobGVuKTtcbiAgICAgICAgbGV0IG9mZnNldCA9IDA7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5jaHVua3MubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGJ5dGVzLnNldCh0aGlzLmNodW5rc1tpXSwgb2Zmc2V0KTtcbiAgICAgICAgICAgIG9mZnNldCArPSB0aGlzLmNodW5rc1tpXS5sZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jaHVua3MgPSBbXTtcbiAgICAgICAgcmV0dXJuIGJ5dGVzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBTdGFydCBhIG5ldyBmb3JrIGZvciBsZW5ndGgtZGVsaW1pdGVkIGRhdGEgbGlrZSBhIG1lc3NhZ2VcbiAgICAgKiBvciBhIHBhY2tlZCByZXBlYXRlZCBmaWVsZC5cbiAgICAgKlxuICAgICAqIE11c3QgYmUgam9pbmVkIGxhdGVyIHdpdGggYGpvaW4oKWAuXG4gICAgICovXG4gICAgZm9yaygpIHtcbiAgICAgICAgdGhpcy5zdGFjay5wdXNoKHsgY2h1bmtzOiB0aGlzLmNodW5rcywgYnVmOiB0aGlzLmJ1ZiB9KTtcbiAgICAgICAgdGhpcy5jaHVua3MgPSBbXTtcbiAgICAgICAgdGhpcy5idWYgPSBbXTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEpvaW4gdGhlIGxhc3QgZm9yay4gV3JpdGUgaXRzIGxlbmd0aCBhbmQgYnl0ZXMsIHRoZW5cbiAgICAgKiByZXR1cm4gdG8gdGhlIHByZXZpb3VzIHN0YXRlLlxuICAgICAqL1xuICAgIGpvaW4oKSB7XG4gICAgICAgIC8vIGdldCBjaHVuayBvZiBmb3JrXG4gICAgICAgIGxldCBjaHVuayA9IHRoaXMuZmluaXNoKCk7XG4gICAgICAgIC8vIHJlc3RvcmUgcHJldmlvdXMgc3RhdGVcbiAgICAgICAgbGV0IHByZXYgPSB0aGlzLnN0YWNrLnBvcCgpO1xuICAgICAgICBpZiAoIXByZXYpXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJpbnZhbGlkIHN0YXRlLCBmb3JrIHN0YWNrIGVtcHR5XCIpO1xuICAgICAgICB0aGlzLmNodW5rcyA9IHByZXYuY2h1bmtzO1xuICAgICAgICB0aGlzLmJ1ZiA9IHByZXYuYnVmO1xuICAgICAgICAvLyB3cml0ZSBsZW5ndGggb2YgY2h1bmsgYXMgdmFyaW50XG4gICAgICAgIHRoaXMudWludDMyKGNodW5rLmJ5dGVMZW5ndGgpO1xuICAgICAgICByZXR1cm4gdGhpcy5yYXcoY2h1bmspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZXMgYSB0YWcgKGZpZWxkIG51bWJlciBhbmQgd2lyZSB0eXBlKS5cbiAgICAgKlxuICAgICAqIEVxdWl2YWxlbnQgdG8gYHVpbnQzMiggKGZpZWxkTm8gPDwgMyB8IHR5cGUpID4+PiAwIClgLlxuICAgICAqXG4gICAgICogR2VuZXJhdGVkIGNvZGUgc2hvdWxkIGNvbXB1dGUgdGhlIHRhZyBhaGVhZCBvZiB0aW1lIGFuZCBjYWxsIGB1aW50MzIoKWAuXG4gICAgICovXG4gICAgdGFnKGZpZWxkTm8sIHR5cGUpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudWludDMyKCgoZmllbGRObyA8PCAzKSB8IHR5cGUpID4+PiAwKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYSBjaHVuayBvZiByYXcgYnl0ZXMuXG4gICAgICovXG4gICAgcmF3KGNodW5rKSB7XG4gICAgICAgIGlmICh0aGlzLmJ1Zi5sZW5ndGgpIHtcbiAgICAgICAgICAgIHRoaXMuY2h1bmtzLnB1c2gobmV3IFVpbnQ4QXJyYXkodGhpcy5idWYpKTtcbiAgICAgICAgICAgIHRoaXMuYnVmID0gW107XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jaHVua3MucHVzaChjaHVuayk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGB1aW50MzJgIHZhbHVlLCBhbiB1bnNpZ25lZCAzMiBiaXQgdmFyaW50LlxuICAgICAqL1xuICAgIHVpbnQzMih2YWx1ZSkge1xuICAgICAgICBhc3NlcnRVSW50MzIodmFsdWUpO1xuICAgICAgICAvLyB3cml0ZSB2YWx1ZSBhcyB2YXJpbnQgMzIsIGlubGluZWQgZm9yIHNwZWVkXG4gICAgICAgIHdoaWxlICh2YWx1ZSA+IDB4N2YpIHtcbiAgICAgICAgICAgIHRoaXMuYnVmLnB1c2goKHZhbHVlICYgMHg3ZikgfCAweDgwKTtcbiAgICAgICAgICAgIHZhbHVlID0gdmFsdWUgPj4+IDc7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5idWYucHVzaCh2YWx1ZSk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBpbnQzMmAgdmFsdWUsIGEgc2lnbmVkIDMyIGJpdCB2YXJpbnQuXG4gICAgICovXG4gICAgaW50MzIodmFsdWUpIHtcbiAgICAgICAgYXNzZXJ0SW50MzIodmFsdWUpO1xuICAgICAgICB2YXJpbnQzMndyaXRlKHZhbHVlLCB0aGlzLmJ1Zik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBib29sYCB2YWx1ZSwgYSB2YXJpYW50LlxuICAgICAqL1xuICAgIGJvb2wodmFsdWUpIHtcbiAgICAgICAgdGhpcy5idWYucHVzaCh2YWx1ZSA/IDEgOiAwKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIGEgYGJ5dGVzYCB2YWx1ZSwgbGVuZ3RoLWRlbGltaXRlZCBhcmJpdHJhcnkgZGF0YS5cbiAgICAgKi9cbiAgICBieXRlcyh2YWx1ZSkge1xuICAgICAgICB0aGlzLnVpbnQzMih2YWx1ZS5ieXRlTGVuZ3RoKTsgLy8gd3JpdGUgbGVuZ3RoIG9mIGNodW5rIGFzIHZhcmludFxuICAgICAgICByZXR1cm4gdGhpcy5yYXcodmFsdWUpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBzdHJpbmdgIHZhbHVlLCBsZW5ndGgtZGVsaW1pdGVkIGRhdGEgY29udmVydGVkIHRvIFVURi04IHRleHQuXG4gICAgICovXG4gICAgc3RyaW5nKHZhbHVlKSB7XG4gICAgICAgIGxldCBjaHVuayA9IHRoaXMudGV4dEVuY29kZXIuZW5jb2RlKHZhbHVlKTtcbiAgICAgICAgdGhpcy51aW50MzIoY2h1bmsuYnl0ZUxlbmd0aCk7IC8vIHdyaXRlIGxlbmd0aCBvZiBjaHVuayBhcyB2YXJpbnRcbiAgICAgICAgcmV0dXJuIHRoaXMucmF3KGNodW5rKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYSBgZmxvYXRgIHZhbHVlLCAzMi1iaXQgZmxvYXRpbmcgcG9pbnQgbnVtYmVyLlxuICAgICAqL1xuICAgIGZsb2F0KHZhbHVlKSB7XG4gICAgICAgIGFzc2VydEZsb2F0MzIodmFsdWUpO1xuICAgICAgICBsZXQgY2h1bmsgPSBuZXcgVWludDhBcnJheSg0KTtcbiAgICAgICAgbmV3IERhdGFWaWV3KGNodW5rLmJ1ZmZlcikuc2V0RmxvYXQzMigwLCB2YWx1ZSwgdHJ1ZSk7XG4gICAgICAgIHJldHVybiB0aGlzLnJhdyhjaHVuayk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFdyaXRlIGEgYGRvdWJsZWAgdmFsdWUsIGEgNjQtYml0IGZsb2F0aW5nIHBvaW50IG51bWJlci5cbiAgICAgKi9cbiAgICBkb3VibGUodmFsdWUpIHtcbiAgICAgICAgbGV0IGNodW5rID0gbmV3IFVpbnQ4QXJyYXkoOCk7XG4gICAgICAgIG5ldyBEYXRhVmlldyhjaHVuay5idWZmZXIpLnNldEZsb2F0NjQoMCwgdmFsdWUsIHRydWUpO1xuICAgICAgICByZXR1cm4gdGhpcy5yYXcoY2h1bmspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBmaXhlZDMyYCB2YWx1ZSwgYW4gdW5zaWduZWQsIGZpeGVkLWxlbmd0aCAzMi1iaXQgaW50ZWdlci5cbiAgICAgKi9cbiAgICBmaXhlZDMyKHZhbHVlKSB7XG4gICAgICAgIGFzc2VydFVJbnQzMih2YWx1ZSk7XG4gICAgICAgIGxldCBjaHVuayA9IG5ldyBVaW50OEFycmF5KDQpO1xuICAgICAgICBuZXcgRGF0YVZpZXcoY2h1bmsuYnVmZmVyKS5zZXRVaW50MzIoMCwgdmFsdWUsIHRydWUpO1xuICAgICAgICByZXR1cm4gdGhpcy5yYXcoY2h1bmspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBzZml4ZWQzMmAgdmFsdWUsIGEgc2lnbmVkLCBmaXhlZC1sZW5ndGggMzItYml0IGludGVnZXIuXG4gICAgICovXG4gICAgc2ZpeGVkMzIodmFsdWUpIHtcbiAgICAgICAgYXNzZXJ0SW50MzIodmFsdWUpO1xuICAgICAgICBsZXQgY2h1bmsgPSBuZXcgVWludDhBcnJheSg0KTtcbiAgICAgICAgbmV3IERhdGFWaWV3KGNodW5rLmJ1ZmZlcikuc2V0SW50MzIoMCwgdmFsdWUsIHRydWUpO1xuICAgICAgICByZXR1cm4gdGhpcy5yYXcoY2h1bmspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBzaW50MzJgIHZhbHVlLCBhIHNpZ25lZCwgemlnemFnLWVuY29kZWQgMzItYml0IHZhcmludC5cbiAgICAgKi9cbiAgICBzaW50MzIodmFsdWUpIHtcbiAgICAgICAgYXNzZXJ0SW50MzIodmFsdWUpO1xuICAgICAgICAvLyB6aWd6YWcgZW5jb2RlXG4gICAgICAgIHZhbHVlID0gKCh2YWx1ZSA8PCAxKSBeICh2YWx1ZSA+PiAzMSkpID4+PiAwO1xuICAgICAgICB2YXJpbnQzMndyaXRlKHZhbHVlLCB0aGlzLmJ1Zik7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBmaXhlZDY0YCB2YWx1ZSwgYSBzaWduZWQsIGZpeGVkLWxlbmd0aCA2NC1iaXQgaW50ZWdlci5cbiAgICAgKi9cbiAgICBzZml4ZWQ2NCh2YWx1ZSkge1xuICAgICAgICBsZXQgY2h1bmsgPSBuZXcgVWludDhBcnJheSg4KSwgdmlldyA9IG5ldyBEYXRhVmlldyhjaHVuay5idWZmZXIpLCB0YyA9IHByb3RvSW50NjQuZW5jKHZhbHVlKTtcbiAgICAgICAgdmlldy5zZXRJbnQzMigwLCB0Yy5sbywgdHJ1ZSk7XG4gICAgICAgIHZpZXcuc2V0SW50MzIoNCwgdGMuaGksIHRydWUpO1xuICAgICAgICByZXR1cm4gdGhpcy5yYXcoY2h1bmspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBmaXhlZDY0YCB2YWx1ZSwgYW4gdW5zaWduZWQsIGZpeGVkLWxlbmd0aCA2NCBiaXQgaW50ZWdlci5cbiAgICAgKi9cbiAgICBmaXhlZDY0KHZhbHVlKSB7XG4gICAgICAgIGxldCBjaHVuayA9IG5ldyBVaW50OEFycmF5KDgpLCB2aWV3ID0gbmV3IERhdGFWaWV3KGNodW5rLmJ1ZmZlciksIHRjID0gcHJvdG9JbnQ2NC51RW5jKHZhbHVlKTtcbiAgICAgICAgdmlldy5zZXRJbnQzMigwLCB0Yy5sbywgdHJ1ZSk7XG4gICAgICAgIHZpZXcuc2V0SW50MzIoNCwgdGMuaGksIHRydWUpO1xuICAgICAgICByZXR1cm4gdGhpcy5yYXcoY2h1bmspO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBXcml0ZSBhIGBpbnQ2NGAgdmFsdWUsIGEgc2lnbmVkIDY0LWJpdCB2YXJpbnQuXG4gICAgICovXG4gICAgaW50NjQodmFsdWUpIHtcbiAgICAgICAgbGV0IHRjID0gcHJvdG9JbnQ2NC5lbmModmFsdWUpO1xuICAgICAgICB2YXJpbnQ2NHdyaXRlKHRjLmxvLCB0Yy5oaSwgdGhpcy5idWYpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYSBgc2ludDY0YCB2YWx1ZSwgYSBzaWduZWQsIHppZy16YWctZW5jb2RlZCA2NC1iaXQgdmFyaW50LlxuICAgICAqL1xuICAgIHNpbnQ2NCh2YWx1ZSkge1xuICAgICAgICBsZXQgdGMgPSBwcm90b0ludDY0LmVuYyh2YWx1ZSksIFxuICAgICAgICAvLyB6aWd6YWcgZW5jb2RlXG4gICAgICAgIHNpZ24gPSB0Yy5oaSA+PiAzMSwgbG8gPSAodGMubG8gPDwgMSkgXiBzaWduLCBoaSA9ICgodGMuaGkgPDwgMSkgfCAodGMubG8gPj4+IDMxKSkgXiBzaWduO1xuICAgICAgICB2YXJpbnQ2NHdyaXRlKGxvLCBoaSwgdGhpcy5idWYpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogV3JpdGUgYSBgdWludDY0YCB2YWx1ZSwgYW4gdW5zaWduZWQgNjQtYml0IHZhcmludC5cbiAgICAgKi9cbiAgICB1aW50NjQodmFsdWUpIHtcbiAgICAgICAgbGV0IHRjID0gcHJvdG9JbnQ2NC51RW5jKHZhbHVlKTtcbiAgICAgICAgdmFyaW50NjR3cml0ZSh0Yy5sbywgdGMuaGksIHRoaXMuYnVmKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIEJpbmFyeVJlYWRlciB7XG4gICAgY29uc3RydWN0b3IoYnVmLCB0ZXh0RGVjb2Rlcikge1xuICAgICAgICB0aGlzLnZhcmludDY0ID0gdmFyaW50NjRyZWFkOyAvLyBkaXJ0eSBjYXN0IGZvciBgdGhpc2BcbiAgICAgICAgLyoqXG4gICAgICAgICAqIFJlYWQgYSBgdWludDMyYCBmaWVsZCwgYW4gdW5zaWduZWQgMzIgYml0IHZhcmludC5cbiAgICAgICAgICovXG4gICAgICAgIHRoaXMudWludDMyID0gdmFyaW50MzJyZWFkOyAvLyBkaXJ0eSBjYXN0IGZvciBgdGhpc2AgYW5kIGFjY2VzcyB0byBwcm90ZWN0ZWQgYGJ1ZmBcbiAgICAgICAgdGhpcy5idWYgPSBidWY7XG4gICAgICAgIHRoaXMubGVuID0gYnVmLmxlbmd0aDtcbiAgICAgICAgdGhpcy5wb3MgPSAwO1xuICAgICAgICB0aGlzLnZpZXcgPSBuZXcgRGF0YVZpZXcoYnVmLmJ1ZmZlciwgYnVmLmJ5dGVPZmZzZXQsIGJ1Zi5ieXRlTGVuZ3RoKTtcbiAgICAgICAgdGhpcy50ZXh0RGVjb2RlciA9IHRleHREZWNvZGVyICE9PSBudWxsICYmIHRleHREZWNvZGVyICE9PSB2b2lkIDAgPyB0ZXh0RGVjb2RlciA6IG5ldyBUZXh0RGVjb2RlcigpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkcyBhIHRhZyAtIGZpZWxkIG51bWJlciBhbmQgd2lyZSB0eXBlLlxuICAgICAqL1xuICAgIHRhZygpIHtcbiAgICAgICAgbGV0IHRhZyA9IHRoaXMudWludDMyKCksIGZpZWxkTm8gPSB0YWcgPj4+IDMsIHdpcmVUeXBlID0gdGFnICYgNztcbiAgICAgICAgaWYgKGZpZWxkTm8gPD0gMCB8fCB3aXJlVHlwZSA8IDAgfHwgd2lyZVR5cGUgPiA1KVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaWxsZWdhbCB0YWc6IGZpZWxkIG5vIFwiICsgZmllbGRObyArIFwiIHdpcmUgdHlwZSBcIiArIHdpcmVUeXBlKTtcbiAgICAgICAgcmV0dXJuIFtmaWVsZE5vLCB3aXJlVHlwZV07XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNraXAgb25lIGVsZW1lbnQgYW5kIHJldHVybiB0aGUgc2tpcHBlZCBkYXRhLlxuICAgICAqXG4gICAgICogV2hlbiBza2lwcGluZyBTdGFydEdyb3VwLCBwcm92aWRlIHRoZSB0YWdzIGZpZWxkIG51bWJlciB0byBjaGVjayBmb3JcbiAgICAgKiBtYXRjaGluZyBmaWVsZCBudW1iZXIgaW4gdGhlIEVuZEdyb3VwIHRhZy5cbiAgICAgKi9cbiAgICBza2lwKHdpcmVUeXBlLCBmaWVsZE5vKSB7XG4gICAgICAgIGxldCBzdGFydCA9IHRoaXMucG9zO1xuICAgICAgICBzd2l0Y2ggKHdpcmVUeXBlKSB7XG4gICAgICAgICAgICBjYXNlIFdpcmVUeXBlLlZhcmludDpcbiAgICAgICAgICAgICAgICB3aGlsZSAodGhpcy5idWZbdGhpcy5wb3MrK10gJiAweDgwKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIGlnbm9yZVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZVxuICAgICAgICAgICAgLy8gQHRzLWlnbm9yZSBUUzcwMjk6IEZhbGx0aHJvdWdoIGNhc2UgaW4gc3dpdGNoXG4gICAgICAgICAgICBjYXNlIFdpcmVUeXBlLkJpdDY0OlxuICAgICAgICAgICAgICAgIHRoaXMucG9zICs9IDQ7XG4gICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmVcbiAgICAgICAgICAgIC8vIEB0cy1pZ25vcmUgVFM3MDI5OiBGYWxsdGhyb3VnaCBjYXNlIGluIHN3aXRjaFxuICAgICAgICAgICAgY2FzZSBXaXJlVHlwZS5CaXQzMjpcbiAgICAgICAgICAgICAgICB0aGlzLnBvcyArPSA0O1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBXaXJlVHlwZS5MZW5ndGhEZWxpbWl0ZWQ6XG4gICAgICAgICAgICAgICAgbGV0IGxlbiA9IHRoaXMudWludDMyKCk7XG4gICAgICAgICAgICAgICAgdGhpcy5wb3MgKz0gbGVuO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBXaXJlVHlwZS5TdGFydEdyb3VwOlxuICAgICAgICAgICAgICAgIGZvciAoOzspIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgW2ZuLCB3dF0gPSB0aGlzLnRhZygpO1xuICAgICAgICAgICAgICAgICAgICBpZiAod3QgPT09IFdpcmVUeXBlLkVuZEdyb3VwKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZmllbGRObyAhPT0gdW5kZWZpbmVkICYmIGZuICE9PSBmaWVsZE5vKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaW52YWxpZCBlbmQgZ3JvdXAgdGFnXCIpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgdGhpcy5za2lwKHd0LCBmbik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJjYW50IHNraXAgd2lyZSB0eXBlIFwiICsgd2lyZVR5cGUpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuYXNzZXJ0Qm91bmRzKCk7XG4gICAgICAgIHJldHVybiB0aGlzLmJ1Zi5zdWJhcnJheShzdGFydCwgdGhpcy5wb3MpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUaHJvd3MgZXJyb3IgaWYgcG9zaXRpb24gaW4gYnl0ZSBhcnJheSBpcyBvdXQgb2YgcmFuZ2UuXG4gICAgICovXG4gICAgYXNzZXJ0Qm91bmRzKCkge1xuICAgICAgICBpZiAodGhpcy5wb3MgPiB0aGlzLmxlbilcbiAgICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKFwicHJlbWF0dXJlIEVPRlwiKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIGBpbnQzMmAgZmllbGQsIGEgc2lnbmVkIDMyIGJpdCB2YXJpbnQuXG4gICAgICovXG4gICAgaW50MzIoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnVpbnQzMigpIHwgMDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIGBzaW50MzJgIGZpZWxkLCBhIHNpZ25lZCwgemlnemFnLWVuY29kZWQgMzItYml0IHZhcmludC5cbiAgICAgKi9cbiAgICBzaW50MzIoKSB7XG4gICAgICAgIGxldCB6emUgPSB0aGlzLnVpbnQzMigpO1xuICAgICAgICAvLyBkZWNvZGUgemlnemFnXG4gICAgICAgIHJldHVybiAoenplID4+PiAxKSBeIC0oenplICYgMSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSBgaW50NjRgIGZpZWxkLCBhIHNpZ25lZCA2NC1iaXQgdmFyaW50LlxuICAgICAqL1xuICAgIGludDY0KCkge1xuICAgICAgICByZXR1cm4gcHJvdG9JbnQ2NC5kZWMoLi4udGhpcy52YXJpbnQ2NCgpKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIGB1aW50NjRgIGZpZWxkLCBhbiB1bnNpZ25lZCA2NC1iaXQgdmFyaW50LlxuICAgICAqL1xuICAgIHVpbnQ2NCgpIHtcbiAgICAgICAgcmV0dXJuIHByb3RvSW50NjQudURlYyguLi50aGlzLnZhcmludDY0KCkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYHNpbnQ2NGAgZmllbGQsIGEgc2lnbmVkLCB6aWctemFnLWVuY29kZWQgNjQtYml0IHZhcmludC5cbiAgICAgKi9cbiAgICBzaW50NjQoKSB7XG4gICAgICAgIGxldCBbbG8sIGhpXSA9IHRoaXMudmFyaW50NjQoKTtcbiAgICAgICAgLy8gZGVjb2RlIHppZyB6YWdcbiAgICAgICAgbGV0IHMgPSAtKGxvICYgMSk7XG4gICAgICAgIGxvID0gKChsbyA+Pj4gMSkgfCAoKGhpICYgMSkgPDwgMzEpKSBeIHM7XG4gICAgICAgIGhpID0gKGhpID4+PiAxKSBeIHM7XG4gICAgICAgIHJldHVybiBwcm90b0ludDY0LmRlYyhsbywgaGkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYGJvb2xgIGZpZWxkLCBhIHZhcmlhbnQuXG4gICAgICovXG4gICAgYm9vbCgpIHtcbiAgICAgICAgbGV0IFtsbywgaGldID0gdGhpcy52YXJpbnQ2NCgpO1xuICAgICAgICByZXR1cm4gbG8gIT09IDAgfHwgaGkgIT09IDA7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSBgZml4ZWQzMmAgZmllbGQsIGFuIHVuc2lnbmVkLCBmaXhlZC1sZW5ndGggMzItYml0IGludGVnZXIuXG4gICAgICovXG4gICAgZml4ZWQzMigpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmlldy5nZXRVaW50MzIoKHRoaXMucG9zICs9IDQpIC0gNCwgdHJ1ZSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlYWQgYSBgc2ZpeGVkMzJgIGZpZWxkLCBhIHNpZ25lZCwgZml4ZWQtbGVuZ3RoIDMyLWJpdCBpbnRlZ2VyLlxuICAgICAqL1xuICAgIHNmaXhlZDMyKCkge1xuICAgICAgICByZXR1cm4gdGhpcy52aWV3LmdldEludDMyKCh0aGlzLnBvcyArPSA0KSAtIDQsIHRydWUpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYGZpeGVkNjRgIGZpZWxkLCBhbiB1bnNpZ25lZCwgZml4ZWQtbGVuZ3RoIDY0IGJpdCBpbnRlZ2VyLlxuICAgICAqL1xuICAgIGZpeGVkNjQoKSB7XG4gICAgICAgIHJldHVybiBwcm90b0ludDY0LnVEZWModGhpcy5zZml4ZWQzMigpLCB0aGlzLnNmaXhlZDMyKCkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYGZpeGVkNjRgIGZpZWxkLCBhIHNpZ25lZCwgZml4ZWQtbGVuZ3RoIDY0LWJpdCBpbnRlZ2VyLlxuICAgICAqL1xuICAgIHNmaXhlZDY0KCkge1xuICAgICAgICByZXR1cm4gcHJvdG9JbnQ2NC5kZWModGhpcy5zZml4ZWQzMigpLCB0aGlzLnNmaXhlZDMyKCkpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYGZsb2F0YCBmaWVsZCwgMzItYml0IGZsb2F0aW5nIHBvaW50IG51bWJlci5cbiAgICAgKi9cbiAgICBmbG9hdCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMudmlldy5nZXRGbG9hdDMyKCh0aGlzLnBvcyArPSA0KSAtIDQsIHRydWUpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYGRvdWJsZWAgZmllbGQsIGEgNjQtYml0IGZsb2F0aW5nIHBvaW50IG51bWJlci5cbiAgICAgKi9cbiAgICBkb3VibGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnZpZXcuZ2V0RmxvYXQ2NCgodGhpcy5wb3MgKz0gOCkgLSA4LCB0cnVlKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVhZCBhIGBieXRlc2AgZmllbGQsIGxlbmd0aC1kZWxpbWl0ZWQgYXJiaXRyYXJ5IGRhdGEuXG4gICAgICovXG4gICAgYnl0ZXMoKSB7XG4gICAgICAgIGxldCBsZW4gPSB0aGlzLnVpbnQzMigpLCBzdGFydCA9IHRoaXMucG9zO1xuICAgICAgICB0aGlzLnBvcyArPSBsZW47XG4gICAgICAgIHRoaXMuYXNzZXJ0Qm91bmRzKCk7XG4gICAgICAgIHJldHVybiB0aGlzLmJ1Zi5zdWJhcnJheShzdGFydCwgc3RhcnQgKyBsZW4pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZWFkIGEgYHN0cmluZ2AgZmllbGQsIGxlbmd0aC1kZWxpbWl0ZWQgZGF0YSBjb252ZXJ0ZWQgdG8gVVRGLTggdGV4dC5cbiAgICAgKi9cbiAgICBzdHJpbmcoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnRleHREZWNvZGVyLmRlY29kZSh0aGlzLmJ5dGVzKCkpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearExtension: () => (/* binding */ clearExtension),\n/* harmony export */   getExtension: () => (/* binding */ getExtension),\n/* harmony export */   hasExtension: () => (/* binding */ hasExtension),\n/* harmony export */   setExtension: () => (/* binding */ setExtension)\n/* harmony export */ });\n/* harmony import */ var _private_assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./private/assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _private_extensions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/extensions.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * Retrieve an extension value from a message.\n *\n * The function never returns undefined. Use hasExtension() to check whether an\n * extension is set. If the extension is not set, this function returns the\n * default value (if one was specified in the protobuf source), or the zero value\n * (for example `0` for numeric types, `[]` for repeated extension fields, and\n * an empty message instance for message fields).\n *\n * Extensions are stored as unknown fields on a message. To mutate an extension\n * value, make sure to store the new value with setExtension() after mutating.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nfunction getExtension(message, extension, options) {\n    assertExtendee(extension, message);\n    const opt = extension.runtime.bin.makeReadOptions(options);\n    const ufs = (0,_private_extensions_js__WEBPACK_IMPORTED_MODULE_0__.filterUnknownFields)(message.getType().runtime.bin.listUnknownFields(message), extension.field);\n    const [container, get] = (0,_private_extensions_js__WEBPACK_IMPORTED_MODULE_0__.createExtensionContainer)(extension);\n    for (const uf of ufs) {\n        extension.runtime.bin.readField(container, opt.readerFactory(uf.data), extension.field, uf.wireType, opt);\n    }\n    return get();\n}\n/**\n * Set an extension value on a message. If the message already has a value for\n * this extension, the value is replaced.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nfunction setExtension(message, extension, value, options) {\n    assertExtendee(extension, message);\n    const readOpt = extension.runtime.bin.makeReadOptions(options);\n    const writeOpt = extension.runtime.bin.makeWriteOptions(options);\n    if (hasExtension(message, extension)) {\n        const ufs = message\n            .getType()\n            .runtime.bin.listUnknownFields(message)\n            .filter((uf) => uf.no != extension.field.no);\n        message.getType().runtime.bin.discardUnknownFields(message);\n        for (const uf of ufs) {\n            message\n                .getType()\n                .runtime.bin.onUnknownField(message, uf.no, uf.wireType, uf.data);\n        }\n    }\n    const writer = writeOpt.writerFactory();\n    let f = extension.field;\n    // Implicit presence does not apply to extensions, see https://github.com/protocolbuffers/protobuf/issues/8234\n    // We patch the field info to use explicit presence:\n    if (!f.opt && !f.repeated && (f.kind == \"enum\" || f.kind == \"scalar\")) {\n        f = Object.assign(Object.assign({}, extension.field), { opt: true });\n    }\n    extension.runtime.bin.writeField(f, value, writer, writeOpt);\n    const reader = readOpt.readerFactory(writer.finish());\n    while (reader.pos < reader.len) {\n        const [no, wireType] = reader.tag();\n        const data = reader.skip(wireType, no);\n        message.getType().runtime.bin.onUnknownField(message, no, wireType, data);\n    }\n}\n/**\n * Remove an extension value from a message.\n *\n * If the extension does not extend the given message, an error is raised.\n */\nfunction clearExtension(message, extension) {\n    assertExtendee(extension, message);\n    if (hasExtension(message, extension)) {\n        const bin = message.getType().runtime.bin;\n        const ufs = bin\n            .listUnknownFields(message)\n            .filter((uf) => uf.no != extension.field.no);\n        bin.discardUnknownFields(message);\n        for (const uf of ufs) {\n            bin.onUnknownField(message, uf.no, uf.wireType, uf.data);\n        }\n    }\n}\n/**\n * Check whether an extension is set on a message.\n */\nfunction hasExtension(message, extension) {\n    const messageType = message.getType();\n    return (extension.extendee.typeName === messageType.typeName &&\n        !!messageType.runtime.bin\n            .listUnknownFields(message)\n            .find((uf) => uf.no == extension.field.no));\n}\nfunction assertExtendee(extension, message) {\n    (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_1__.assert)(extension.extendee.typeName == message.getType().typeName, `extension ${extension.typeName} can only be applied to message ${extension.extendee.typeName}`);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/duration_pb.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/duration_pb.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Duration: () => (/* binding */ Duration)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../proto-int64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _proto3_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../proto3.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * A Duration represents a signed, fixed-length span of time represented\n * as a count of seconds and fractions of seconds at nanosecond\n * resolution. It is independent of any calendar and concepts like \"day\"\n * or \"month\". It is related to Timestamp in that the difference between\n * two Timestamp values is a Duration and it can be added or subtracted\n * from a Timestamp. Range is approximately +-10,000 years.\n *\n * # Examples\n *\n * Example 1: Compute Duration from two Timestamps in pseudo code.\n *\n *     Timestamp start = ...;\n *     Timestamp end = ...;\n *     Duration duration = ...;\n *\n *     duration.seconds = end.seconds - start.seconds;\n *     duration.nanos = end.nanos - start.nanos;\n *\n *     if (duration.seconds < 0 && duration.nanos > 0) {\n *       duration.seconds += 1;\n *       duration.nanos -= **********;\n *     } else if (duration.seconds > 0 && duration.nanos < 0) {\n *       duration.seconds -= 1;\n *       duration.nanos += **********;\n *     }\n *\n * Example 2: Compute Timestamp from Timestamp + Duration in pseudo code.\n *\n *     Timestamp start = ...;\n *     Duration duration = ...;\n *     Timestamp end = ...;\n *\n *     end.seconds = start.seconds + duration.seconds;\n *     end.nanos = start.nanos + duration.nanos;\n *\n *     if (end.nanos < 0) {\n *       end.seconds -= 1;\n *       end.nanos += **********;\n *     } else if (end.nanos >= **********) {\n *       end.seconds += 1;\n *       end.nanos -= **********;\n *     }\n *\n * Example 3: Compute Duration from datetime.timedelta in Python.\n *\n *     td = datetime.timedelta(days=3, minutes=10)\n *     duration = Duration()\n *     duration.FromTimedelta(td)\n *\n * # JSON Mapping\n *\n * In JSON format, the Duration type is encoded as a string rather than an\n * object, where the string ends in the suffix \"s\" (indicating seconds) and\n * is preceded by the number of seconds, with nanoseconds expressed as\n * fractional seconds. For example, 3 seconds with 0 nanoseconds should be\n * encoded in JSON format as \"3s\", while 3 seconds and 1 nanosecond should\n * be expressed in JSON format as \"3.000000001s\", and 3 seconds and 1\n * microsecond should be expressed in JSON format as \"3.000001s\".\n *\n *\n * @generated from message google.protobuf.Duration\n */\nclass Duration extends _message_js__WEBPACK_IMPORTED_MODULE_0__.Message {\n    constructor(data) {\n        super();\n        /**\n         * Signed seconds of the span of time. Must be from -315,576,000,000\n         * to +315,576,000,000 inclusive. Note: these bounds are computed from:\n         * 60 sec/min * 60 min/hr * 24 hr/day * 365.25 days/year * 10000 years\n         *\n         * @generated from field: int64 seconds = 1;\n         */\n        this.seconds = _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.zero;\n        /**\n         * Signed fractions of a second at nanosecond resolution of the span\n         * of time. Durations less than one second are represented with a 0\n         * `seconds` field and a positive or negative `nanos` field. For durations\n         * of one second or more, a non-zero value for the `nanos` field must be\n         * of the same sign as the `seconds` field. Must be from -999,999,999\n         * to +999,999,999 inclusive.\n         *\n         * @generated from field: int32 nanos = 2;\n         */\n        this.nanos = 0;\n        _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.util.initPartial(data, this);\n    }\n    fromJson(json, options) {\n        if (typeof json !== \"string\") {\n            throw new Error(`cannot decode google.protobuf.Duration from JSON: ${_proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.json.debug(json)}`);\n        }\n        const match = json.match(/^(-?[0-9]+)(?:\\.([0-9]+))?s/);\n        if (match === null) {\n            throw new Error(`cannot decode google.protobuf.Duration from JSON: ${_proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.json.debug(json)}`);\n        }\n        const longSeconds = Number(match[1]);\n        if (longSeconds > 315576000000 || longSeconds < -315576000000) {\n            throw new Error(`cannot decode google.protobuf.Duration from JSON: ${_proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.json.debug(json)}`);\n        }\n        this.seconds = _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.parse(longSeconds);\n        if (typeof match[2] == \"string\") {\n            const nanosStr = match[2] + \"0\".repeat(9 - match[2].length);\n            this.nanos = parseInt(nanosStr);\n            if (longSeconds < 0 || Object.is(longSeconds, -0)) {\n                this.nanos = -this.nanos;\n            }\n        }\n        return this;\n    }\n    toJson(options) {\n        if (Number(this.seconds) > 315576000000 || Number(this.seconds) < -315576000000) {\n            throw new Error(`cannot encode google.protobuf.Duration to JSON: value out of range`);\n        }\n        let text = this.seconds.toString();\n        if (this.nanos !== 0) {\n            let nanosStr = Math.abs(this.nanos).toString();\n            nanosStr = \"0\".repeat(9 - nanosStr.length) + nanosStr;\n            if (nanosStr.substring(3) === \"000000\") {\n                nanosStr = nanosStr.substring(0, 3);\n            }\n            else if (nanosStr.substring(6) === \"000\") {\n                nanosStr = nanosStr.substring(0, 6);\n            }\n            text += \".\" + nanosStr;\n            if (this.nanos < 0 && Number(this.seconds) == 0) {\n                text = \"-\" + text;\n            }\n        }\n        return text + \"s\";\n    }\n    static fromBinary(bytes, options) {\n        return new Duration().fromBinary(bytes, options);\n    }\n    static fromJson(jsonValue, options) {\n        return new Duration().fromJson(jsonValue, options);\n    }\n    static fromJsonString(jsonString, options) {\n        return new Duration().fromJsonString(jsonString, options);\n    }\n    static equals(a, b) {\n        return _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.util.equals(Duration, a, b);\n    }\n}\nDuration.runtime = _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3;\nDuration.typeName = \"google.protobuf.Duration\";\nDuration.fields = _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.util.newFieldList(() => [\n    { no: 1, name: \"seconds\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"nanos\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/duration_pb.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/timestamp_pb.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/timestamp_pb.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Timestamp: () => (/* binding */ Timestamp)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../proto-int64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _proto3_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../proto3.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * A Timestamp represents a point in time independent of any time zone or local\n * calendar, encoded as a count of seconds and fractions of seconds at\n * nanosecond resolution. The count is relative to an epoch at UTC midnight on\n * January 1, 1970, in the proleptic Gregorian calendar which extends the\n * Gregorian calendar backwards to year one.\n *\n * All minutes are 60 seconds long. Leap seconds are \"smeared\" so that no leap\n * second table is needed for interpretation, using a [24-hour linear\n * smear](https://developers.google.com/time/smear).\n *\n * The range is from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59.999999999Z. By\n * restricting to that range, we ensure that we can convert to and from [RFC\n * 3339](https://www.ietf.org/rfc/rfc3339.txt) date strings.\n *\n * # Examples\n *\n * Example 1: Compute Timestamp from POSIX `time()`.\n *\n *     Timestamp timestamp;\n *     timestamp.set_seconds(time(NULL));\n *     timestamp.set_nanos(0);\n *\n * Example 2: Compute Timestamp from POSIX `gettimeofday()`.\n *\n *     struct timeval tv;\n *     gettimeofday(&tv, NULL);\n *\n *     Timestamp timestamp;\n *     timestamp.set_seconds(tv.tv_sec);\n *     timestamp.set_nanos(tv.tv_usec * 1000);\n *\n * Example 3: Compute Timestamp from Win32 `GetSystemTimeAsFileTime()`.\n *\n *     FILETIME ft;\n *     GetSystemTimeAsFileTime(&ft);\n *     UINT64 ticks = (((UINT64)ft.dwHighDateTime) << 32) | ft.dwLowDateTime;\n *\n *     // A Windows tick is 100 nanoseconds. Windows epoch 1601-01-01T00:00:00Z\n *     // is 11644473600 seconds before Unix epoch 1970-01-01T00:00:00Z.\n *     Timestamp timestamp;\n *     timestamp.set_seconds((INT64) ((ticks / 10000000) - 11644473600LL));\n *     timestamp.set_nanos((INT32) ((ticks % 10000000) * 100));\n *\n * Example 4: Compute Timestamp from Java `System.currentTimeMillis()`.\n *\n *     long millis = System.currentTimeMillis();\n *\n *     Timestamp timestamp = Timestamp.newBuilder().setSeconds(millis / 1000)\n *         .setNanos((int) ((millis % 1000) * 1000000)).build();\n *\n * Example 5: Compute Timestamp from Java `Instant.now()`.\n *\n *     Instant now = Instant.now();\n *\n *     Timestamp timestamp =\n *         Timestamp.newBuilder().setSeconds(now.getEpochSecond())\n *             .setNanos(now.getNano()).build();\n *\n * Example 6: Compute Timestamp from current time in Python.\n *\n *     timestamp = Timestamp()\n *     timestamp.GetCurrentTime()\n *\n * # JSON Mapping\n *\n * In JSON format, the Timestamp type is encoded as a string in the\n * [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format. That is, the\n * format is \"{year}-{month}-{day}T{hour}:{min}:{sec}[.{frac_sec}]Z\"\n * where {year} is always expressed using four digits while {month}, {day},\n * {hour}, {min}, and {sec} are zero-padded to two digits each. The fractional\n * seconds, which can go up to 9 digits (i.e. up to 1 nanosecond resolution),\n * are optional. The \"Z\" suffix indicates the timezone (\"UTC\"); the timezone\n * is required. A proto3 JSON serializer should always use UTC (as indicated by\n * \"Z\") when printing the Timestamp type and a proto3 JSON parser should be\n * able to accept both UTC and other timezones (as indicated by an offset).\n *\n * For example, \"2017-01-15T01:30:15.01Z\" encodes 15.01 seconds past\n * 01:30 UTC on January 15, 2017.\n *\n * In JavaScript, one can convert a Date object to this format using the\n * standard\n * [toISOString()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toISOString)\n * method. In Python, a standard `datetime.datetime` object can be converted\n * to this format using\n * [`strftime`](https://docs.python.org/2/library/time.html#time.strftime) with\n * the time format spec '%Y-%m-%dT%H:%M:%S.%fZ'. Likewise, in Java, one can use\n * the Joda Time's [`ISODateTimeFormat.dateTime()`](\n * http://joda-time.sourceforge.net/apidocs/org/joda/time/format/ISODateTimeFormat.html#dateTime()\n * ) to obtain a formatter capable of generating timestamps in this format.\n *\n *\n * @generated from message google.protobuf.Timestamp\n */\nclass Timestamp extends _message_js__WEBPACK_IMPORTED_MODULE_0__.Message {\n    constructor(data) {\n        super();\n        /**\n         * Represents seconds of UTC time since Unix epoch\n         * 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to\n         * 9999-12-31T23:59:59Z inclusive.\n         *\n         * @generated from field: int64 seconds = 1;\n         */\n        this.seconds = _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.zero;\n        /**\n         * Non-negative fractions of a second at nanosecond resolution. Negative\n         * second values with fractions must still have non-negative nanos values\n         * that count forward in time. Must be from 0 to 999,999,999\n         * inclusive.\n         *\n         * @generated from field: int32 nanos = 2;\n         */\n        this.nanos = 0;\n        _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.util.initPartial(data, this);\n    }\n    fromJson(json, options) {\n        if (typeof json !== \"string\") {\n            throw new Error(`cannot decode google.protobuf.Timestamp from JSON: ${_proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.json.debug(json)}`);\n        }\n        const matches = json.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);\n        if (!matches) {\n            throw new Error(`cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string`);\n        }\n        const ms = Date.parse(matches[1] + \"-\" + matches[2] + \"-\" + matches[3] + \"T\" + matches[4] + \":\" + matches[5] + \":\" + matches[6] + (matches[8] ? matches[8] : \"Z\"));\n        if (Number.isNaN(ms)) {\n            throw new Error(`cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string`);\n        }\n        if (ms < Date.parse(\"0001-01-01T00:00:00Z\") || ms > Date.parse(\"9999-12-31T23:59:59Z\")) {\n            throw new Error(`cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);\n        }\n        this.seconds = _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.parse(ms / 1000);\n        this.nanos = 0;\n        if (matches[7]) {\n            this.nanos = (parseInt(\"1\" + matches[7] + \"0\".repeat(9 - matches[7].length)) - **********);\n        }\n        return this;\n    }\n    toJson(options) {\n        const ms = Number(this.seconds) * 1000;\n        if (ms < Date.parse(\"0001-01-01T00:00:00Z\") || ms > Date.parse(\"9999-12-31T23:59:59Z\")) {\n            throw new Error(`cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);\n        }\n        if (this.nanos < 0) {\n            throw new Error(`cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative`);\n        }\n        let z = \"Z\";\n        if (this.nanos > 0) {\n            const nanosStr = (this.nanos + **********).toString().substring(1);\n            if (nanosStr.substring(3) === \"000000\") {\n                z = \".\" + nanosStr.substring(0, 3) + \"Z\";\n            }\n            else if (nanosStr.substring(6) === \"000\") {\n                z = \".\" + nanosStr.substring(0, 6) + \"Z\";\n            }\n            else {\n                z = \".\" + nanosStr + \"Z\";\n            }\n        }\n        return new Date(ms).toISOString().replace(\".000Z\", z);\n    }\n    toDate() {\n        return new Date(Number(this.seconds) * 1000 + Math.ceil(this.nanos / 1000000));\n    }\n    static now() {\n        return Timestamp.fromDate(new Date());\n    }\n    static fromDate(date) {\n        const ms = date.getTime();\n        return new Timestamp({\n            seconds: _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.parse(Math.floor(ms / 1000)),\n            nanos: (ms % 1000) * 1000000,\n        });\n    }\n    static fromBinary(bytes, options) {\n        return new Timestamp().fromBinary(bytes, options);\n    }\n    static fromJson(jsonValue, options) {\n        return new Timestamp().fromJson(jsonValue, options);\n    }\n    static fromJsonString(jsonString, options) {\n        return new Timestamp().fromJsonString(jsonString, options);\n    }\n    static equals(a, b) {\n        return _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.util.equals(Timestamp, a, b);\n    }\n}\nTimestamp.runtime = _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3;\nTimestamp.typeName = \"google.protobuf.Timestamp\";\nTimestamp.fields = _proto3_js__WEBPACK_IMPORTED_MODULE_2__.proto3.util.newFieldList(() => [\n    { no: 1, name: \"seconds\", kind: \"scalar\", T: 3 /* ScalarType.INT64 */ },\n    { no: 2, name: \"nanos\", kind: \"scalar\", T: 5 /* ScalarType.INT32 */ },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/timestamp_pb.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   int64FromString: () => (/* binding */ int64FromString),\n/* harmony export */   int64ToString: () => (/* binding */ int64ToString),\n/* harmony export */   uInt64ToString: () => (/* binding */ uInt64ToString),\n/* harmony export */   varint32read: () => (/* binding */ varint32read),\n/* harmony export */   varint32write: () => (/* binding */ varint32write),\n/* harmony export */   varint64read: () => (/* binding */ varint64read),\n/* harmony export */   varint64write: () => (/* binding */ varint64write)\n/* harmony export */ });\n// Copyright 2008 Google Inc.  All rights reserved.\n//\n// Redistribution and use in source and binary forms, with or without\n// modification, are permitted provided that the following conditions are\n// met:\n//\n// * Redistributions of source code must retain the above copyright\n// notice, this list of conditions and the following disclaimer.\n// * Redistributions in binary form must reproduce the above\n// copyright notice, this list of conditions and the following disclaimer\n// in the documentation and/or other materials provided with the\n// distribution.\n// * Neither the name of Google Inc. nor the names of its\n// contributors may be used to endorse or promote products derived from\n// this software without specific prior written permission.\n//\n// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n// \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n//\n// Code generated by the Protocol Buffer compiler is owned by the owner\n// of the input file used when generating it.  This code is not\n// standalone and requires a support library to be linked with it.  This\n// support library is itself covered by the above license.\n/* eslint-disable prefer-const,@typescript-eslint/restrict-plus-operands */\n/**\n * Read a 64 bit varint as two JS numbers.\n *\n * Returns tuple:\n * [0]: low bits\n * [1]: high bits\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L175\n */\nfunction varint64read() {\n    let lowBits = 0;\n    let highBits = 0;\n    for (let shift = 0; shift < 28; shift += 7) {\n        let b = this.buf[this.pos++];\n        lowBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    let middleByte = this.buf[this.pos++];\n    // last four bits of the first 32 bit number\n    lowBits |= (middleByte & 0x0f) << 28;\n    // 3 upper bits are part of the next 32 bit number\n    highBits = (middleByte & 0x70) >> 4;\n    if ((middleByte & 0x80) == 0) {\n        this.assertBounds();\n        return [lowBits, highBits];\n    }\n    for (let shift = 3; shift <= 31; shift += 7) {\n        let b = this.buf[this.pos++];\n        highBits |= (b & 0x7f) << shift;\n        if ((b & 0x80) == 0) {\n            this.assertBounds();\n            return [lowBits, highBits];\n        }\n    }\n    throw new Error(\"invalid varint\");\n}\n/**\n * Write a 64 bit varint, given as two JS numbers, to the given bytes array.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/writer.js#L344\n */\nfunction varint64write(lo, hi, bytes) {\n    for (let i = 0; i < 28; i = i + 7) {\n        const shift = lo >>> i;\n        const hasNext = !(shift >>> 7 == 0 && hi == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    const splitBits = ((lo >>> 28) & 0x0f) | ((hi & 0x07) << 4);\n    const hasMoreBits = !(hi >> 3 == 0);\n    bytes.push((hasMoreBits ? splitBits | 0x80 : splitBits) & 0xff);\n    if (!hasMoreBits) {\n        return;\n    }\n    for (let i = 3; i < 31; i = i + 7) {\n        const shift = hi >>> i;\n        const hasNext = !(shift >>> 7 == 0);\n        const byte = (hasNext ? shift | 0x80 : shift) & 0xff;\n        bytes.push(byte);\n        if (!hasNext) {\n            return;\n        }\n    }\n    bytes.push((hi >>> 31) & 0x01);\n}\n// constants for binary math\nconst TWO_PWR_32_DBL = 0x100000000;\n/**\n * Parse decimal string of 64 bit integer value as two JS numbers.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64FromString(dec) {\n    // Check for minus sign.\n    const minus = dec[0] === \"-\";\n    if (minus) {\n        dec = dec.slice(1);\n    }\n    // Work 6 decimal digits at a time, acting like we're converting base 1e6\n    // digits to binary. This is safe to do with floating point math because\n    // Number.isSafeInteger(ALL_32_BITS * 1e6) == true.\n    const base = 1e6;\n    let lowBits = 0;\n    let highBits = 0;\n    function add1e6digit(begin, end) {\n        // Note: Number('') is 0.\n        const digit1e6 = Number(dec.slice(begin, end));\n        highBits *= base;\n        lowBits = lowBits * base + digit1e6;\n        // Carry bits from lowBits to\n        if (lowBits >= TWO_PWR_32_DBL) {\n            highBits = highBits + ((lowBits / TWO_PWR_32_DBL) | 0);\n            lowBits = lowBits % TWO_PWR_32_DBL;\n        }\n    }\n    add1e6digit(-24, -18);\n    add1e6digit(-18, -12);\n    add1e6digit(-12, -6);\n    add1e6digit(-6);\n    return minus ? negate(lowBits, highBits) : newBits(lowBits, highBits);\n}\n/**\n * Losslessly converts a 64-bit signed integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction int64ToString(lo, hi) {\n    let bits = newBits(lo, hi);\n    // If we're treating the input as a signed value and the high bit is set, do\n    // a manual two's complement conversion before the decimal conversion.\n    const negative = (bits.hi & 0x80000000);\n    if (negative) {\n        bits = negate(bits.lo, bits.hi);\n    }\n    const result = uInt64ToString(bits.lo, bits.hi);\n    return negative ? \"-\" + result : result;\n}\n/**\n * Losslessly converts a 64-bit unsigned integer in 32:32 split representation\n * into a decimal string.\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf-javascript/blob/a428c58273abad07c66071d9753bc4d1289de426/experimental/runtime/int64.js#L10\n */\nfunction uInt64ToString(lo, hi) {\n    ({ lo, hi } = toUnsigned(lo, hi));\n    // Skip the expensive conversion if the number is small enough to use the\n    // built-in conversions.\n    // Number.MAX_SAFE_INTEGER = 0x001FFFFF FFFFFFFF, thus any number with\n    // highBits <= 0x1FFFFF can be safely expressed with a double and retain\n    // integer precision.\n    // Proven by: Number.isSafeInteger(0x1FFFFF * 2**32 + 0xFFFFFFFF) == true.\n    if (hi <= 0x1FFFFF) {\n        return String(TWO_PWR_32_DBL * hi + lo);\n    }\n    // What this code is doing is essentially converting the input number from\n    // base-2 to base-1e7, which allows us to represent the 64-bit range with\n    // only 3 (very large) digits. Those digits are then trivial to convert to\n    // a base-10 string.\n    // The magic numbers used here are -\n    // 2^24 = 16777216 = (1,6777216) in base-1e7.\n    // 2^48 = 281474976710656 = (2,8147497,6710656) in base-1e7.\n    // Split 32:32 representation into 16:24:24 representation so our\n    // intermediate digits don't overflow.\n    const low = lo & 0xFFFFFF;\n    const mid = ((lo >>> 24) | (hi << 8)) & 0xFFFFFF;\n    const high = (hi >> 16) & 0xFFFF;\n    // Assemble our three base-1e7 digits, ignoring carries. The maximum\n    // value in a digit at this step is representable as a 48-bit integer, which\n    // can be stored in a 64-bit floating point number.\n    let digitA = low + (mid * 6777216) + (high * 6710656);\n    let digitB = mid + (high * 8147497);\n    let digitC = (high * 2);\n    // Apply carries from A to B and from B to C.\n    const base = 10000000;\n    if (digitA >= base) {\n        digitB += Math.floor(digitA / base);\n        digitA %= base;\n    }\n    if (digitB >= base) {\n        digitC += Math.floor(digitB / base);\n        digitB %= base;\n    }\n    // If digitC is 0, then we should have returned in the trivial code path\n    // at the top for non-safe integers. Given this, we can assume both digitB\n    // and digitA need leading zeros.\n    return digitC.toString() + decimalFrom1e7WithLeadingZeros(digitB) +\n        decimalFrom1e7WithLeadingZeros(digitA);\n}\nfunction toUnsigned(lo, hi) {\n    return { lo: lo >>> 0, hi: hi >>> 0 };\n}\nfunction newBits(lo, hi) {\n    return { lo: lo | 0, hi: hi | 0 };\n}\n/**\n * Returns two's compliment negation of input.\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Bitwise_Operators#Signed_32-bit_integers\n */\nfunction negate(lowBits, highBits) {\n    highBits = ~highBits;\n    if (lowBits) {\n        lowBits = ~lowBits + 1;\n    }\n    else {\n        // If lowBits is 0, then bitwise-not is 0xFFFFFFFF,\n        // adding 1 to that, results in 0x100000000, which leaves\n        // the low bits 0x0 and simply adds one to the high bits.\n        highBits += 1;\n    }\n    return newBits(lowBits, highBits);\n}\n/**\n * Returns decimal representation of digit1e7 with leading zeros.\n */\nconst decimalFrom1e7WithLeadingZeros = (digit1e7) => {\n    const partial = String(digit1e7);\n    return \"0000000\".slice(partial.length) + partial;\n};\n/**\n * Write a 32 bit varint, signed or unsigned. Same as `varint64write(0, value, bytes)`\n *\n * Copyright 2008 Google Inc.  All rights reserved.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/1b18833f4f2a2f681f4e4a25cdf3b0a43115ec26/js/binary/encoder.js#L144\n */\nfunction varint32write(value, bytes) {\n    if (value >= 0) {\n        // write value as varint 32\n        while (value > 0x7f) {\n            bytes.push((value & 0x7f) | 0x80);\n            value = value >>> 7;\n        }\n        bytes.push(value);\n    }\n    else {\n        for (let i = 0; i < 9; i++) {\n            bytes.push((value & 127) | 128);\n            value = value >> 7;\n        }\n        bytes.push(1);\n    }\n}\n/**\n * Read an unsigned 32 bit varint.\n *\n * See https://github.com/protocolbuffers/protobuf/blob/8a71927d74a4ce34efe2d8769fda198f52d20d12/js/experimental/runtime/kernel/buffer_decoder.js#L220\n */\nfunction varint32read() {\n    let b = this.buf[this.pos++];\n    let result = b & 0x7f;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 7;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 14;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    b = this.buf[this.pos++];\n    result |= (b & 0x7f) << 21;\n    if ((b & 0x80) == 0) {\n        this.assertBounds();\n        return result;\n    }\n    // Extract only last 4 bits\n    b = this.buf[this.pos++];\n    result |= (b & 0x0f) << 28;\n    for (let readBytes = 5; (b & 0x80) !== 0 && readBytes < 10; readBytes++)\n        b = this.buf[this.pos++];\n    if ((b & 0x80) != 0)\n        throw new Error(\"invalid varint\");\n    this.assertBounds();\n    // Result can have 32 bits, convert it to unsigned\n    return result >>> 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js":
/*!****************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/is-message.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isMessage: () => (/* binding */ isMessage)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Check whether the given object is any subtype of Message or is a specific\n * Message by passing the type.\n *\n * Just like `instanceof`, `isMessage` narrows the type. The advantage of\n * `isMessage` is that it compares identity by the message type name, not by\n * class identity. This makes it robust against the dual package hazard and\n * similar situations, where the same message is duplicated.\n *\n * This function is _mostly_ equivalent to the `instanceof` operator. For\n * example, `isMessage(foo, MyMessage)` is the same as `foo instanceof MyMessage`,\n * and `isMessage(foo)` is the same as `foo instanceof Message`. In most cases,\n * `isMessage` should be preferred over `instanceof`.\n *\n * However, due to the fact that `isMessage` does not use class identity, there\n * are subtle differences between this function and `instanceof`. Notably,\n * calling `isMessage` on an explicit type of Message will return false.\n */\nfunction isMessage(arg, type) {\n    if (arg === null || typeof arg != \"object\") {\n        return false;\n    }\n    if (!Object.getOwnPropertyNames(_message_js__WEBPACK_IMPORTED_MODULE_0__.Message.prototype).every((m) => m in arg && typeof arg[m] == \"function\")) {\n        return false;\n    }\n    const actualType = arg.getType();\n    if (actualType === null ||\n        typeof actualType != \"function\" ||\n        !(\"typeName\" in actualType) ||\n        typeof actualType.typeName != \"string\") {\n        return false;\n    }\n    return type === undefined ? true : actualType.typeName == type.typeName;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/message.js":
/*!*************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/message.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Message: () => (/* binding */ Message)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Message is the base class of every message, generated, or created at\n * runtime.\n *\n * It is _not_ safe to extend this class. If you want to create a message at\n * run time, use proto3.makeMessageType().\n */\nclass Message {\n    /**\n     * Compare with a message of the same type.\n     * Note that this function disregards extensions and unknown fields.\n     */\n    equals(other) {\n        return this.getType().runtime.util.equals(this.getType(), this, other);\n    }\n    /**\n     * Create a deep copy.\n     */\n    clone() {\n        return this.getType().runtime.util.clone(this);\n    }\n    /**\n     * Parse from binary data, merging fields.\n     *\n     * Repeated fields are appended. Map entries are added, overwriting\n     * existing keys.\n     *\n     * If a message field is already present, it will be merged with the\n     * new data.\n     */\n    fromBinary(bytes, options) {\n        const type = this.getType(), format = type.runtime.bin, opt = format.makeReadOptions(options);\n        format.readMessage(this, opt.readerFactory(bytes), bytes.byteLength, opt);\n        return this;\n    }\n    /**\n     * Parse a message from a JSON value.\n     */\n    fromJson(jsonValue, options) {\n        const type = this.getType(), format = type.runtime.json, opt = format.makeReadOptions(options);\n        format.readMessage(type, jsonValue, opt, this);\n        return this;\n    }\n    /**\n     * Parse a message from a JSON string.\n     */\n    fromJsonString(jsonString, options) {\n        let json;\n        try {\n            json = JSON.parse(jsonString);\n        }\n        catch (e) {\n            throw new Error(`cannot decode ${this.getType().typeName} from JSON: ${e instanceof Error ? e.message : String(e)}`);\n        }\n        return this.fromJson(json, options);\n    }\n    /**\n     * Serialize the message to binary data.\n     */\n    toBinary(options) {\n        const type = this.getType(), bin = type.runtime.bin, opt = bin.makeWriteOptions(options), writer = opt.writerFactory();\n        bin.writeMessage(this, writer, opt);\n        return writer.finish();\n    }\n    /**\n     * Serialize the message to a JSON value, a JavaScript value that can be\n     * passed to JSON.stringify().\n     */\n    toJson(options) {\n        const type = this.getType(), json = type.runtime.json, opt = json.makeWriteOptions(options);\n        return json.writeMessage(this, opt);\n    }\n    /**\n     * Serialize the message to a JSON string.\n     */\n    toJsonString(options) {\n        var _a;\n        const value = this.toJson(options);\n        return JSON.stringify(value, null, (_a = options === null || options === void 0 ? void 0 : options.prettySpaces) !== null && _a !== void 0 ? _a : 0);\n    }\n    /**\n     * Override for serialization behavior. This will be invoked when calling\n     * JSON.stringify on this message (i.e. JSON.stringify(msg)).\n     *\n     * Note that this will not serialize google.protobuf.Any with a packed\n     * message because the protobuf JSON format specifies that it needs to be\n     * unpacked, and this is only possible with a type registry to look up the\n     * message type.  As a result, attempting to serialize a message with this\n     * type will throw an Error.\n     *\n     * This method is protected because you should not need to invoke it\n     * directly -- instead use JSON.stringify or toJsonString for\n     * stringified JSON.  Alternatively, if actual JSON is desired, you should\n     * use toJson.\n     */\n    toJSON() {\n        return this.toJson({\n            emitDefaultValues: true,\n        });\n    }\n    /**\n     * Retrieve the MessageType of this message - a singleton that represents\n     * the protobuf message declaration and provides metadata for reflection-\n     * based operations.\n     */\n    getType() {\n        // Any class that extends Message _must_ provide a complete static\n        // implementation of MessageType.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-return\n        return Object.getPrototypeOf(this).constructor;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js":
/*!********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   assertFloat32: () => (/* binding */ assertFloat32),\n/* harmony export */   assertInt32: () => (/* binding */ assertInt32),\n/* harmony export */   assertUInt32: () => (/* binding */ assertUInt32)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Assert that condition is truthy or throw error (with message)\n */\nfunction assert(condition, msg) {\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions -- we want the implicit conversion to boolean\n    if (!condition) {\n        throw new Error(msg);\n    }\n}\nconst FLOAT32_MAX = 3.4028234663852886e38, FLOAT32_MIN = -3.4028234663852886e38, UINT32_MAX = 0xffffffff, INT32_MAX = 0x7fffffff, INT32_MIN = -0x80000000;\n/**\n * Assert a valid signed protobuf 32-bit integer.\n */\nfunction assertInt32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid int 32: \" + typeof arg);\n    if (!Number.isInteger(arg) || arg > INT32_MAX || arg < INT32_MIN)\n        throw new Error(\"invalid int 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n/**\n * Assert a valid unsigned protobuf 32-bit integer.\n */\nfunction assertUInt32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid uint 32: \" + typeof arg);\n    if (!Number.isInteger(arg) || arg > UINT32_MAX || arg < 0)\n        throw new Error(\"invalid uint 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n/**\n * Assert a valid protobuf float value.\n */\nfunction assertFloat32(arg) {\n    if (typeof arg !== \"number\")\n        throw new Error(\"invalid float 32: \" + typeof arg);\n    if (!Number.isFinite(arg))\n        return;\n    if (arg > FLOAT32_MAX || arg < FLOAT32_MIN)\n        throw new Error(\"invalid float 32: \" + arg); // eslint-disable-line @typescript-eslint/restrict-plus-operands -- we want the implicit conversion to string\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeBinaryFormat: () => (/* binding */ makeBinaryFormat),\n/* harmony export */   writeMapEntry: () => (/* binding */ writeMapEntry)\n/* harmony export */ });\n/* harmony import */ var _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../binary-encoding.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/binary-encoding.js\");\n/* harmony import */ var _field_wrapper_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./field-wrapper.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js\");\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scalars.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _reflect_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reflect.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../scalar.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../is-message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n\n\n\n/* eslint-disable prefer-const,no-case-declarations,@typescript-eslint/no-explicit-any,@typescript-eslint/no-unsafe-argument,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-return */\nconst unknownFieldsSymbol = Symbol(\"@bufbuild/protobuf/unknown-fields\");\n// Default options for parsing binary data.\nconst readDefaults = {\n    readUnknownFields: true,\n    readerFactory: (bytes) => new _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.BinaryReader(bytes),\n};\n// Default options for serializing binary data.\nconst writeDefaults = {\n    writeUnknownFields: true,\n    writerFactory: () => new _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.BinaryWriter(),\n};\nfunction makeReadOptions(options) {\n    return options ? Object.assign(Object.assign({}, readDefaults), options) : readDefaults;\n}\nfunction makeWriteOptions(options) {\n    return options ? Object.assign(Object.assign({}, writeDefaults), options) : writeDefaults;\n}\nfunction makeBinaryFormat() {\n    return {\n        makeReadOptions,\n        makeWriteOptions,\n        listUnknownFields(message) {\n            var _a;\n            return (_a = message[unknownFieldsSymbol]) !== null && _a !== void 0 ? _a : [];\n        },\n        discardUnknownFields(message) {\n            delete message[unknownFieldsSymbol];\n        },\n        writeUnknownFields(message, writer) {\n            const m = message;\n            const c = m[unknownFieldsSymbol];\n            if (c) {\n                for (const f of c) {\n                    writer.tag(f.no, f.wireType).raw(f.data);\n                }\n            }\n        },\n        onUnknownField(message, no, wireType, data) {\n            const m = message;\n            if (!Array.isArray(m[unknownFieldsSymbol])) {\n                m[unknownFieldsSymbol] = [];\n            }\n            m[unknownFieldsSymbol].push({ no, wireType, data });\n        },\n        readMessage(message, reader, lengthOrEndTagFieldNo, options, delimitedMessageEncoding) {\n            const type = message.getType();\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            const end = delimitedMessageEncoding\n                ? reader.len\n                : reader.pos + lengthOrEndTagFieldNo;\n            let fieldNo, wireType;\n            while (reader.pos < end) {\n                [fieldNo, wireType] = reader.tag();\n                if (delimitedMessageEncoding === true &&\n                    wireType == _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.EndGroup) {\n                    break;\n                }\n                const field = type.fields.find(fieldNo);\n                if (!field) {\n                    const data = reader.skip(wireType, fieldNo);\n                    if (options.readUnknownFields) {\n                        this.onUnknownField(message, fieldNo, wireType, data);\n                    }\n                    continue;\n                }\n                readField(message, reader, field, wireType, options);\n            }\n            if (delimitedMessageEncoding && // eslint-disable-line @typescript-eslint/strict-boolean-expressions\n                (wireType != _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.EndGroup || fieldNo !== lengthOrEndTagFieldNo)) {\n                throw new Error(`invalid end group tag`);\n            }\n        },\n        readField,\n        writeMessage(message, writer, options) {\n            const type = message.getType();\n            for (const field of type.fields.byNumber()) {\n                if (!(0,_reflect_js__WEBPACK_IMPORTED_MODULE_1__.isFieldSet)(field, message)) {\n                    if (field.req) {\n                        throw new Error(`cannot encode field ${type.typeName}.${field.name} to binary: required field not set`);\n                    }\n                    continue;\n                }\n                const value = field.oneof\n                    ? message[field.oneof.localName].value\n                    : message[field.localName];\n                writeField(field, value, writer, options);\n            }\n            if (options.writeUnknownFields) {\n                this.writeUnknownFields(message, writer);\n            }\n            return writer;\n        },\n        writeField(field, value, writer, options) {\n            // The behavior of our internal function has changed, it does no longer\n            // accept `undefined` values for singular scalar and map.\n            // For backwards-compatibility, we support the old form that is part of\n            // the public API through the interface BinaryFormat.\n            if (value === undefined) {\n                return undefined;\n            }\n            writeField(field, value, writer, options);\n        },\n    };\n}\nfunction readField(target, // eslint-disable-line @typescript-eslint/no-explicit-any -- `any` is the best choice for dynamic access\nreader, field, wireType, options) {\n    let { repeated, localName } = field;\n    if (field.oneof) {\n        target = target[field.oneof.localName];\n        if (target.case != localName) {\n            delete target.value;\n        }\n        target.case = localName;\n        localName = \"value\";\n    }\n    switch (field.kind) {\n        case \"scalar\":\n        case \"enum\":\n            const scalarType = field.kind == \"enum\" ? _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32 : field.T;\n            let read = readScalar;\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison -- acceptable since it's covered by tests\n            if (field.kind == \"scalar\" && field.L > 0) {\n                read = readScalarLTString;\n            }\n            if (repeated) {\n                let arr = target[localName]; // safe to assume presence of array, oneof cannot contain repeated values\n                const isPacked = wireType == _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited &&\n                    scalarType != _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.STRING &&\n                    scalarType != _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES;\n                if (isPacked) {\n                    let e = reader.uint32() + reader.pos;\n                    while (reader.pos < e) {\n                        arr.push(read(reader, scalarType));\n                    }\n                }\n                else {\n                    arr.push(read(reader, scalarType));\n                }\n            }\n            else {\n                target[localName] = read(reader, scalarType);\n            }\n            break;\n        case \"message\":\n            const messageType = field.T;\n            if (repeated) {\n                // safe to assume presence of array, oneof cannot contain repeated values\n                target[localName].push(readMessageField(reader, new messageType(), options, field));\n            }\n            else {\n                if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_3__.isMessage)(target[localName])) {\n                    readMessageField(reader, target[localName], options, field);\n                }\n                else {\n                    target[localName] = readMessageField(reader, new messageType(), options, field);\n                    if (messageType.fieldWrapper && !field.oneof && !field.repeated) {\n                        target[localName] = messageType.fieldWrapper.unwrapField(target[localName]);\n                    }\n                }\n            }\n            break;\n        case \"map\":\n            let [mapKey, mapVal] = readMapEntry(field, reader, options);\n            // safe to assume presence of map object, oneof cannot contain repeated values\n            target[localName][mapKey] = mapVal;\n            break;\n    }\n}\n// Read a message, avoiding MessageType.fromBinary() to re-use the\n// BinaryReadOptions and the IBinaryReader.\nfunction readMessageField(reader, message, options, field) {\n    const format = message.getType().runtime.bin;\n    const delimited = field === null || field === void 0 ? void 0 : field.delimited;\n    format.readMessage(message, reader, delimited ? field.no : reader.uint32(), // eslint-disable-line @typescript-eslint/strict-boolean-expressions\n    options, delimited);\n    return message;\n}\n// Read a map field, expecting key field = 1, value field = 2\nfunction readMapEntry(field, reader, options) {\n    const length = reader.uint32(), end = reader.pos + length;\n    let key, val;\n    while (reader.pos < end) {\n        const [fieldNo] = reader.tag();\n        switch (fieldNo) {\n            case 1:\n                key = readScalar(reader, field.K);\n                break;\n            case 2:\n                switch (field.V.kind) {\n                    case \"scalar\":\n                        val = readScalar(reader, field.V.T);\n                        break;\n                    case \"enum\":\n                        val = reader.int32();\n                        break;\n                    case \"message\":\n                        val = readMessageField(reader, new field.V.T(), options, undefined);\n                        break;\n                }\n                break;\n        }\n    }\n    if (key === undefined) {\n        key = (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.scalarZeroValue)(field.K, _scalar_js__WEBPACK_IMPORTED_MODULE_2__.LongType.BIGINT);\n    }\n    if (typeof key != \"string\" && typeof key != \"number\") {\n        key = key.toString();\n    }\n    if (val === undefined) {\n        switch (field.V.kind) {\n            case \"scalar\":\n                val = (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.scalarZeroValue)(field.V.T, _scalar_js__WEBPACK_IMPORTED_MODULE_2__.LongType.BIGINT);\n                break;\n            case \"enum\":\n                val = field.V.T.values[0].no;\n                break;\n            case \"message\":\n                val = new field.V.T();\n                break;\n        }\n    }\n    return [key, val];\n}\n// Read a scalar value, but return 64 bit integral types (int64, uint64,\n// sint64, fixed64, sfixed64) as string instead of bigint.\nfunction readScalarLTString(reader, type) {\n    const v = readScalar(reader, type);\n    return typeof v == \"bigint\" ? v.toString() : v;\n}\n// Does not use scalarTypeInfo() for better performance.\nfunction readScalar(reader, type) {\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.STRING:\n            return reader.string();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BOOL:\n            return reader.bool();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.DOUBLE:\n            return reader.double();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FLOAT:\n            return reader.float();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32:\n            return reader.int32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT64:\n            return reader.int64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.UINT64:\n            return reader.uint64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED64:\n            return reader.fixed64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES:\n            return reader.bytes();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED32:\n            return reader.fixed32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED32:\n            return reader.sfixed32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED64:\n            return reader.sfixed64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SINT64:\n            return reader.sint64();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.UINT32:\n            return reader.uint32();\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SINT32:\n            return reader.sint32();\n    }\n}\nfunction writeField(field, value, writer, options) {\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value !== undefined);\n    const repeated = field.repeated;\n    switch (field.kind) {\n        case \"scalar\":\n        case \"enum\":\n            let scalarType = field.kind == \"enum\" ? _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32 : field.T;\n            if (repeated) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(Array.isArray(value));\n                if (field.packed) {\n                    writePacked(writer, scalarType, field.no, value);\n                }\n                else {\n                    for (const item of value) {\n                        writeScalar(writer, scalarType, field.no, item);\n                    }\n                }\n            }\n            else {\n                writeScalar(writer, scalarType, field.no, value);\n            }\n            break;\n        case \"message\":\n            if (repeated) {\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(Array.isArray(value));\n                for (const item of value) {\n                    writeMessageField(writer, options, field, item);\n                }\n            }\n            else {\n                writeMessageField(writer, options, field, value);\n            }\n            break;\n        case \"map\":\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"object\" && value != null);\n            for (const [key, val] of Object.entries(value)) {\n                writeMapEntry(writer, options, field, key, val);\n            }\n            break;\n    }\n}\nfunction writeMapEntry(writer, options, field, key, value) {\n    writer.tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited);\n    writer.fork();\n    // javascript only allows number or string for object properties\n    // we convert from our representation to the protobuf type\n    let keyValue = key;\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- we deliberately handle just the special cases for map keys\n    switch (field.K) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.UINT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SINT32:\n            keyValue = Number.parseInt(key);\n            break;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BOOL:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(key == \"true\" || key == \"false\");\n            keyValue = key == \"true\";\n            break;\n    }\n    // write key, expecting key field number = 1\n    writeScalar(writer, field.K, 1, keyValue);\n    // write value, expecting value field number = 2\n    switch (field.V.kind) {\n        case \"scalar\":\n            writeScalar(writer, field.V.T, 2, value);\n            break;\n        case \"enum\":\n            writeScalar(writer, _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, 2, value);\n            break;\n        case \"message\":\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value !== undefined);\n            writer.tag(2, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited).bytes(value.toBinary(options));\n            break;\n    }\n    writer.join();\n}\n// Value must not be undefined\nfunction writeMessageField(writer, options, field, value) {\n    const message = (0,_field_wrapper_js__WEBPACK_IMPORTED_MODULE_6__.wrapField)(field.T, value);\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n    if (field.delimited)\n        writer\n            .tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.StartGroup)\n            .raw(message.toBinary(options))\n            .tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.EndGroup);\n    else\n        writer\n            .tag(field.no, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited)\n            .bytes(message.toBinary(options));\n}\nfunction writeScalar(writer, type, fieldNo, value) {\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value !== undefined);\n    let [wireType, method] = scalarTypeInfo(type);\n    writer.tag(fieldNo, wireType)[method](value);\n}\nfunction writePacked(writer, type, fieldNo, value) {\n    if (!value.length) {\n        return;\n    }\n    writer.tag(fieldNo, _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited).fork();\n    let [, method] = scalarTypeInfo(type);\n    for (let i = 0; i < value.length; i++) {\n        writer[method](value[i]);\n    }\n    writer.join();\n}\n/**\n * Get information for writing a scalar value.\n *\n * Returns tuple:\n * [0]: appropriate WireType\n * [1]: name of the appropriate method of IBinaryWriter\n * [2]: whether the given value is a default value for proto3 semantics\n *\n * If argument `value` is omitted, [2] is always false.\n */\n// TODO replace call-sites writeScalar() and writePacked(), then remove\nfunction scalarTypeInfo(type) {\n    let wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.Varint;\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- INT32, UINT32, SINT32 are covered by the defaults\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.STRING:\n            wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.LengthDelimited;\n            break;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.DOUBLE:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED64:\n            wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.Bit64;\n            break;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.FLOAT:\n            wireType = _binary_encoding_js__WEBPACK_IMPORTED_MODULE_0__.WireType.Bit32;\n            break;\n    }\n    const method = _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType[type].toLowerCase();\n    return [wireType, method];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js":
/*!******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnumType: () => (/* binding */ getEnumType),\n/* harmony export */   makeEnum: () => (/* binding */ makeEnum),\n/* harmony export */   makeEnumType: () => (/* binding */ makeEnumType),\n/* harmony export */   setEnumType: () => (/* binding */ setEnumType)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\nconst enumTypeSymbol = Symbol(\"@bufbuild/protobuf/enum-type\");\n/**\n * Get reflection information from a generated enum.\n * If this function is called on something other than a generated\n * enum, it raises an error.\n */\nfunction getEnumType(enumObject) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-explicit-any\n    const t = enumObject[enumTypeSymbol];\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_0__.assert)(t, \"missing enum type on enum object\");\n    return t; // eslint-disable-line @typescript-eslint/no-unsafe-return\n}\n/**\n * Sets reflection information on a generated enum.\n */\nfunction setEnumType(enumObject, typeName, values, opt) {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n    enumObject[enumTypeSymbol] = makeEnumType(typeName, values.map((v) => ({\n        no: v.no,\n        name: v.name,\n        localName: enumObject[v.no],\n    })), opt);\n}\n/**\n * Create a new EnumType with the given values.\n */\nfunction makeEnumType(typeName, values, \n// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_opt) {\n    const names = Object.create(null);\n    const numbers = Object.create(null);\n    const normalValues = [];\n    for (const value of values) {\n        // We do not surface options at this time\n        // const value: EnumValueInfo = {...v, options: v.options ?? emptyReadonlyObject};\n        const n = normalizeEnumValue(value);\n        normalValues.push(n);\n        names[value.name] = n;\n        numbers[value.no] = n;\n    }\n    return {\n        typeName,\n        values: normalValues,\n        // We do not surface options at this time\n        // options: opt?.options ?? Object.create(null),\n        findName(name) {\n            return names[name];\n        },\n        findNumber(no) {\n            return numbers[no];\n        },\n    };\n}\n/**\n * Create a new enum object with the given values.\n * Sets reflection information.\n */\nfunction makeEnum(typeName, values, opt) {\n    const enumObject = {};\n    for (const value of values) {\n        const n = normalizeEnumValue(value);\n        enumObject[n.localName] = n.no;\n        enumObject[n.no] = n.localName;\n    }\n    setEnumType(enumObject, typeName, values, opt);\n    return enumObject;\n}\nfunction normalizeEnumValue(value) {\n    if (\"localName\" in value) {\n        return value;\n    }\n    return Object.assign(Object.assign({}, value), { localName: value.name });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL3ByaXZhdGUvZW51bS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ3FDO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLElBQUksa0RBQU07QUFDVixjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsWUFBWSx1QkFBdUI7QUFDNUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vbm9kZV9tb2R1bGVzL0BidWZidWlsZC9wcm90b2J1Zi9kaXN0L2VzbS9wcml2YXRlL2VudW0uanM/MGEyMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAyMS0yMDI0IEJ1ZiBUZWNobm9sb2dpZXMsIEluYy5cbi8vXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuaW1wb3J0IHsgYXNzZXJ0IH0gZnJvbSBcIi4vYXNzZXJ0LmpzXCI7XG5jb25zdCBlbnVtVHlwZVN5bWJvbCA9IFN5bWJvbChcIkBidWZidWlsZC9wcm90b2J1Zi9lbnVtLXR5cGVcIik7XG4vKipcbiAqIEdldCByZWZsZWN0aW9uIGluZm9ybWF0aW9uIGZyb20gYSBnZW5lcmF0ZWQgZW51bS5cbiAqIElmIHRoaXMgZnVuY3Rpb24gaXMgY2FsbGVkIG9uIHNvbWV0aGluZyBvdGhlciB0aGFuIGEgZ2VuZXJhdGVkXG4gKiBlbnVtLCBpdCByYWlzZXMgYW4gZXJyb3IuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFbnVtVHlwZShlbnVtT2JqZWN0KSB7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtbWVtYmVyLWFjY2VzcyxAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5zYWZlLWFzc2lnbm1lbnQsQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuICAgIGNvbnN0IHQgPSBlbnVtT2JqZWN0W2VudW1UeXBlU3ltYm9sXTtcbiAgICBhc3NlcnQodCwgXCJtaXNzaW5nIGVudW0gdHlwZSBvbiBlbnVtIG9iamVjdFwiKTtcbiAgICByZXR1cm4gdDsgLy8gZXNsaW50LWRpc2FibGUtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5zYWZlLXJldHVyblxufVxuLyoqXG4gKiBTZXRzIHJlZmxlY3Rpb24gaW5mb3JtYXRpb24gb24gYSBnZW5lcmF0ZWQgZW51bS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNldEVudW1UeXBlKGVudW1PYmplY3QsIHR5cGVOYW1lLCB2YWx1ZXMsIG9wdCkge1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5zYWZlLW1lbWJlci1hY2Nlc3MsIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICBlbnVtT2JqZWN0W2VudW1UeXBlU3ltYm9sXSA9IG1ha2VFbnVtVHlwZSh0eXBlTmFtZSwgdmFsdWVzLm1hcCgodikgPT4gKHtcbiAgICAgICAgbm86IHYubm8sXG4gICAgICAgIG5hbWU6IHYubmFtZSxcbiAgICAgICAgbG9jYWxOYW1lOiBlbnVtT2JqZWN0W3Yubm9dLFxuICAgIH0pKSwgb3B0KTtcbn1cbi8qKlxuICogQ3JlYXRlIGEgbmV3IEVudW1UeXBlIHdpdGggdGhlIGdpdmVuIHZhbHVlcy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIG1ha2VFbnVtVHlwZSh0eXBlTmFtZSwgdmFsdWVzLCBcbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW51c2VkLXZhcnNcbl9vcHQpIHtcbiAgICBjb25zdCBuYW1lcyA9IE9iamVjdC5jcmVhdGUobnVsbCk7XG4gICAgY29uc3QgbnVtYmVycyA9IE9iamVjdC5jcmVhdGUobnVsbCk7XG4gICAgY29uc3Qgbm9ybWFsVmFsdWVzID0gW107XG4gICAgZm9yIChjb25zdCB2YWx1ZSBvZiB2YWx1ZXMpIHtcbiAgICAgICAgLy8gV2UgZG8gbm90IHN1cmZhY2Ugb3B0aW9ucyBhdCB0aGlzIHRpbWVcbiAgICAgICAgLy8gY29uc3QgdmFsdWU6IEVudW1WYWx1ZUluZm8gPSB7Li4udiwgb3B0aW9uczogdi5vcHRpb25zID8/IGVtcHR5UmVhZG9ubHlPYmplY3R9O1xuICAgICAgICBjb25zdCBuID0gbm9ybWFsaXplRW51bVZhbHVlKHZhbHVlKTtcbiAgICAgICAgbm9ybWFsVmFsdWVzLnB1c2gobik7XG4gICAgICAgIG5hbWVzW3ZhbHVlLm5hbWVdID0gbjtcbiAgICAgICAgbnVtYmVyc1t2YWx1ZS5ub10gPSBuO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICB0eXBlTmFtZSxcbiAgICAgICAgdmFsdWVzOiBub3JtYWxWYWx1ZXMsXG4gICAgICAgIC8vIFdlIGRvIG5vdCBzdXJmYWNlIG9wdGlvbnMgYXQgdGhpcyB0aW1lXG4gICAgICAgIC8vIG9wdGlvbnM6IG9wdD8ub3B0aW9ucyA/PyBPYmplY3QuY3JlYXRlKG51bGwpLFxuICAgICAgICBmaW5kTmFtZShuYW1lKSB7XG4gICAgICAgICAgICByZXR1cm4gbmFtZXNbbmFtZV07XG4gICAgICAgIH0sXG4gICAgICAgIGZpbmROdW1iZXIobm8pIHtcbiAgICAgICAgICAgIHJldHVybiBudW1iZXJzW25vXTtcbiAgICAgICAgfSxcbiAgICB9O1xufVxuLyoqXG4gKiBDcmVhdGUgYSBuZXcgZW51bSBvYmplY3Qgd2l0aCB0aGUgZ2l2ZW4gdmFsdWVzLlxuICogU2V0cyByZWZsZWN0aW9uIGluZm9ybWF0aW9uLlxuICovXG5leHBvcnQgZnVuY3Rpb24gbWFrZUVudW0odHlwZU5hbWUsIHZhbHVlcywgb3B0KSB7XG4gICAgY29uc3QgZW51bU9iamVjdCA9IHt9O1xuICAgIGZvciAoY29uc3QgdmFsdWUgb2YgdmFsdWVzKSB7XG4gICAgICAgIGNvbnN0IG4gPSBub3JtYWxpemVFbnVtVmFsdWUodmFsdWUpO1xuICAgICAgICBlbnVtT2JqZWN0W24ubG9jYWxOYW1lXSA9IG4ubm87XG4gICAgICAgIGVudW1PYmplY3Rbbi5ub10gPSBuLmxvY2FsTmFtZTtcbiAgICB9XG4gICAgc2V0RW51bVR5cGUoZW51bU9iamVjdCwgdHlwZU5hbWUsIHZhbHVlcywgb3B0KTtcbiAgICByZXR1cm4gZW51bU9iamVjdDtcbn1cbmZ1bmN0aW9uIG5vcm1hbGl6ZUVudW1WYWx1ZSh2YWx1ZSkge1xuICAgIGlmIChcImxvY2FsTmFtZVwiIGluIHZhbHVlKSB7XG4gICAgICAgIHJldHVybiB2YWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdmFsdWUpLCB7IGxvY2FsTmFtZTogdmFsdWUubmFtZSB9KTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createExtensionContainer: () => (/* binding */ createExtensionContainer),\n/* harmony export */   filterUnknownFields: () => (/* binding */ filterUnknownFields),\n/* harmony export */   makeExtension: () => (/* binding */ makeExtension)\n/* harmony export */ });\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scalars.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * Create a new extension using the given runtime.\n */\nfunction makeExtension(runtime, typeName, extendee, field) {\n    let fi;\n    return {\n        typeName,\n        extendee,\n        get field() {\n            if (!fi) {\n                const i = (typeof field == \"function\" ? field() : field);\n                i.name = typeName.split(\".\").pop();\n                i.jsonName = `[${typeName}]`;\n                fi = runtime.util.newFieldList([i]).list()[0];\n            }\n            return fi;\n        },\n        runtime,\n    };\n}\n/**\n * Create a container that allows us to read extension fields into it with the\n * same logic as regular fields.\n */\nfunction createExtensionContainer(extension) {\n    const localName = extension.field.localName;\n    const container = Object.create(null);\n    container[localName] = initExtensionField(extension);\n    return [container, () => container[localName]];\n}\nfunction initExtensionField(ext) {\n    const field = ext.field;\n    if (field.repeated) {\n        return [];\n    }\n    if (field.default !== undefined) {\n        return field.default;\n    }\n    switch (field.kind) {\n        case \"enum\":\n            return field.T.values[0].no;\n        case \"scalar\":\n            return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_0__.scalarZeroValue)(field.T, field.L);\n        case \"message\":\n            // eslint-disable-next-line no-case-declarations\n            const T = field.T, value = new T();\n            return T.fieldWrapper ? T.fieldWrapper.unwrapField(value) : value;\n        case \"map\":\n            throw \"map fields are not allowed to be extensions\";\n    }\n}\n/**\n * Helper to filter unknown fields, optimized based on field type.\n */\nfunction filterUnknownFields(unknownFields, field) {\n    if (!field.repeated && (field.kind == \"enum\" || field.kind == \"scalar\")) {\n        // singular scalar fields do not merge, we pick the last\n        for (let i = unknownFields.length - 1; i >= 0; --i) {\n            if (unknownFields[i].no == field.no) {\n                return [unknownFields[i]];\n            }\n        }\n        return [];\n    }\n    return unknownFields.filter((uf) => uf.no === field.no);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js":
/*!************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InternalFieldList: () => (/* binding */ InternalFieldList)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nclass InternalFieldList {\n    constructor(fields, normalizer) {\n        this._fields = fields;\n        this._normalizer = normalizer;\n    }\n    findJsonName(jsonName) {\n        if (!this.jsonNames) {\n            const t = {};\n            for (const f of this.list()) {\n                t[f.jsonName] = t[f.name] = f;\n            }\n            this.jsonNames = t;\n        }\n        return this.jsonNames[jsonName];\n    }\n    find(fieldNo) {\n        if (!this.numbers) {\n            const t = {};\n            for (const f of this.list()) {\n                t[f.no] = f;\n            }\n            this.numbers = t;\n        }\n        return this.numbers[fieldNo];\n    }\n    list() {\n        if (!this.all) {\n            this.all = this._normalizer(this._fields);\n        }\n        return this.all;\n    }\n    byNumber() {\n        if (!this.numbersAsc) {\n            this.numbersAsc = this.list()\n                .concat()\n                .sort((a, b) => a.no - b.no);\n        }\n        return this.numbersAsc;\n    }\n    byMember() {\n        if (!this.members) {\n            this.members = [];\n            const a = this.members;\n            let o;\n            for (const f of this.list()) {\n                if (f.oneof) {\n                    if (f.oneof !== o) {\n                        o = f.oneof;\n                        a.push(o);\n                    }\n                }\n                else {\n                    a.push(f);\n                }\n            }\n        }\n        return this.members;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeFieldInfos: () => (/* binding */ normalizeFieldInfos)\n/* harmony export */ });\n/* harmony import */ var _field_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./field.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field.js\");\n/* harmony import */ var _names_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./names.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../scalar.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * Convert a collection of field info to an array of normalized FieldInfo.\n *\n * The argument `packedByDefault` specifies whether fields that do not specify\n * `packed` should be packed (proto3) or unpacked (proto2).\n */\nfunction normalizeFieldInfos(fieldInfos, packedByDefault) {\n    var _a, _b, _c, _d, _e, _f;\n    const r = [];\n    let o;\n    for (const field of typeof fieldInfos == \"function\"\n        ? fieldInfos()\n        : fieldInfos) {\n        const f = field;\n        f.localName = (0,_names_js__WEBPACK_IMPORTED_MODULE_0__.localFieldName)(field.name, field.oneof !== undefined);\n        f.jsonName = (_a = field.jsonName) !== null && _a !== void 0 ? _a : (0,_names_js__WEBPACK_IMPORTED_MODULE_0__.fieldJsonName)(field.name);\n        f.repeated = (_b = field.repeated) !== null && _b !== void 0 ? _b : false;\n        if (field.kind == \"scalar\") {\n            f.L = (_c = field.L) !== null && _c !== void 0 ? _c : _scalar_js__WEBPACK_IMPORTED_MODULE_1__.LongType.BIGINT;\n        }\n        f.delimited = (_d = field.delimited) !== null && _d !== void 0 ? _d : false;\n        f.req = (_e = field.req) !== null && _e !== void 0 ? _e : false;\n        f.opt = (_f = field.opt) !== null && _f !== void 0 ? _f : false;\n        if (field.packed === undefined) {\n            if (packedByDefault) {\n                f.packed =\n                    field.kind == \"enum\" ||\n                        (field.kind == \"scalar\" &&\n                            field.T != _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.BYTES &&\n                            field.T != _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.STRING);\n            }\n            else {\n                f.packed = false;\n            }\n        }\n        // We do not surface options at this time\n        // f.options = field.options ?? emptyReadonlyObject;\n        if (field.oneof !== undefined) {\n            const ooname = typeof field.oneof == \"string\" ? field.oneof : field.oneof.name;\n            if (!o || o.name != ooname) {\n                o = new _field_js__WEBPACK_IMPORTED_MODULE_2__.InternalOneofInfo(ooname);\n            }\n            f.oneof = o;\n            o.addField(f);\n        }\n        r.push(f);\n    }\n    return r;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUnwrappedFieldType: () => (/* binding */ getUnwrappedFieldType),\n/* harmony export */   wrapField: () => (/* binding */ wrapField)\n/* harmony export */ });\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../scalar.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../is-message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n/**\n * Wrap a primitive message field value in its corresponding wrapper\n * message. This function is idempotent.\n */\nfunction wrapField(type, value) {\n    if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_0__.isMessage)(value) || !type.fieldWrapper) {\n        return value;\n    }\n    return type.fieldWrapper.wrapField(value);\n}\n/**\n * If the given field uses one of the well-known wrapper types, return\n * the primitive type it wraps.\n */\nfunction getUnwrappedFieldType(field) {\n    if (field.fieldKind !== \"message\") {\n        return undefined;\n    }\n    if (field.repeated) {\n        return undefined;\n    }\n    if (field.oneof != undefined) {\n        return undefined;\n    }\n    return wktWrapperToScalarType[field.message.typeName];\n}\nconst wktWrapperToScalarType = {\n    \"google.protobuf.DoubleValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.DOUBLE,\n    \"google.protobuf.FloatValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.FLOAT,\n    \"google.protobuf.Int64Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.INT64,\n    \"google.protobuf.UInt64Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.UINT64,\n    \"google.protobuf.Int32Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.INT32,\n    \"google.protobuf.UInt32Value\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.UINT32,\n    \"google.protobuf.BoolValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.BOOL,\n    \"google.protobuf.StringValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.STRING,\n    \"google.protobuf.BytesValue\": _scalar_js__WEBPACK_IMPORTED_MODULE_1__.ScalarType.BYTES,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/field.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InternalOneofInfo: () => (/* binding */ InternalOneofInfo)\n/* harmony export */ });\n/* harmony import */ var _names_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./names.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js\");\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\nclass InternalOneofInfo {\n    constructor(name) {\n        this.kind = \"oneof\";\n        this.repeated = false;\n        this.packed = false;\n        this.opt = false;\n        this.req = false;\n        this.default = undefined;\n        this.fields = [];\n        this.name = name;\n        this.localName = (0,_names_js__WEBPACK_IMPORTED_MODULE_0__.localOneofName)(name);\n    }\n    addField(field) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_1__.assert)(field.oneof === this, `field ${field.name} not one of ${this.name}`);\n        this.fields.push(field);\n    }\n    findField(localName) {\n        if (!this._lookup) {\n            this._lookup = Object.create(null);\n            for (let i = 0; i < this.fields.length; i++) {\n                this._lookup[this.fields[i].localName] = this.fields[i];\n            }\n        }\n        return this._lookup[localName];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeJsonFormat: () => (/* binding */ makeJsonFormat)\n/* harmony export */ });\n/* harmony import */ var _assert_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../proto-int64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _proto_base64_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../proto-base64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js\");\n/* harmony import */ var _extensions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./extensions.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\");\n/* harmony import */ var _extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../extension-accessor.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/extension-accessor.js\");\n/* harmony import */ var _reflect_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reflect.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js\");\n/* harmony import */ var _field_wrapper_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./field-wrapper.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-wrapper.js\");\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scalars.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../scalar.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../is-message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n\n\n\n\n\n\n\n/* eslint-disable no-case-declarations,@typescript-eslint/no-unsafe-argument,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call */\n// Default options for parsing JSON.\nconst jsonReadDefaults = {\n    ignoreUnknownFields: false,\n};\n// Default options for serializing to JSON.\nconst jsonWriteDefaults = {\n    emitDefaultValues: false,\n    enumAsInteger: false,\n    useProtoFieldName: false,\n    prettySpaces: 0,\n};\nfunction makeReadOptions(options) {\n    return options ? Object.assign(Object.assign({}, jsonReadDefaults), options) : jsonReadDefaults;\n}\nfunction makeWriteOptions(options) {\n    return options ? Object.assign(Object.assign({}, jsonWriteDefaults), options) : jsonWriteDefaults;\n}\nconst tokenNull = Symbol();\nconst tokenIgnoredUnknownEnum = Symbol();\nfunction makeJsonFormat() {\n    return {\n        makeReadOptions,\n        makeWriteOptions,\n        readMessage(type, json, options, message) {\n            if (json == null || Array.isArray(json) || typeof json != \"object\") {\n                throw new Error(`cannot decode message ${type.typeName} from JSON: ${debugJsonValue(json)}`);\n            }\n            message = message !== null && message !== void 0 ? message : new type();\n            const oneofSeen = new Map();\n            const registry = options.typeRegistry;\n            for (const [jsonKey, jsonValue] of Object.entries(json)) {\n                const field = type.fields.findJsonName(jsonKey);\n                if (field) {\n                    if (field.oneof) {\n                        if (jsonValue === null && field.kind == \"scalar\") {\n                            // see conformance test Required.Proto3.JsonInput.OneofFieldNull{First,Second}\n                            continue;\n                        }\n                        const seen = oneofSeen.get(field.oneof);\n                        if (seen !== undefined) {\n                            throw new Error(`cannot decode message ${type.typeName} from JSON: multiple keys for oneof \"${field.oneof.name}\" present: \"${seen}\", \"${jsonKey}\"`);\n                        }\n                        oneofSeen.set(field.oneof, jsonKey);\n                    }\n                    readField(message, jsonValue, field, options, type);\n                }\n                else {\n                    let found = false;\n                    if ((registry === null || registry === void 0 ? void 0 : registry.findExtension) &&\n                        jsonKey.startsWith(\"[\") &&\n                        jsonKey.endsWith(\"]\")) {\n                        const ext = registry.findExtension(jsonKey.substring(1, jsonKey.length - 1));\n                        if (ext && ext.extendee.typeName == type.typeName) {\n                            found = true;\n                            const [container, get] = (0,_extensions_js__WEBPACK_IMPORTED_MODULE_0__.createExtensionContainer)(ext);\n                            readField(container, jsonValue, ext.field, options, ext);\n                            // We pass on the options as BinaryReadOptions/BinaryWriteOptions,\n                            // so that users can bring their own binary reader and writer factories\n                            // if necessary.\n                            (0,_extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__.setExtension)(message, ext, get(), options);\n                        }\n                    }\n                    if (!found && !options.ignoreUnknownFields) {\n                        throw new Error(`cannot decode message ${type.typeName} from JSON: key \"${jsonKey}\" is unknown`);\n                    }\n                }\n            }\n            return message;\n        },\n        writeMessage(message, options) {\n            const type = message.getType();\n            const json = {};\n            let field;\n            try {\n                for (field of type.fields.byNumber()) {\n                    if (!(0,_reflect_js__WEBPACK_IMPORTED_MODULE_2__.isFieldSet)(field, message)) {\n                        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n                        if (field.req) {\n                            throw `required field not set`;\n                        }\n                        if (!options.emitDefaultValues) {\n                            continue;\n                        }\n                        if (!canEmitFieldDefaultValue(field)) {\n                            continue;\n                        }\n                    }\n                    const value = field.oneof\n                        ? message[field.oneof.localName].value\n                        : message[field.localName];\n                    const jsonValue = writeField(field, value, options);\n                    if (jsonValue !== undefined) {\n                        json[options.useProtoFieldName ? field.name : field.jsonName] =\n                            jsonValue;\n                    }\n                }\n                const registry = options.typeRegistry;\n                if (registry === null || registry === void 0 ? void 0 : registry.findExtensionFor) {\n                    for (const uf of type.runtime.bin.listUnknownFields(message)) {\n                        const ext = registry.findExtensionFor(type.typeName, uf.no);\n                        if (ext && (0,_extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__.hasExtension)(message, ext)) {\n                            // We pass on the options as BinaryReadOptions, so that users can bring their own\n                            // binary reader factory if necessary.\n                            const value = (0,_extension_accessor_js__WEBPACK_IMPORTED_MODULE_1__.getExtension)(message, ext, options);\n                            const jsonValue = writeField(ext.field, value, options);\n                            if (jsonValue !== undefined) {\n                                json[ext.field.jsonName] = jsonValue;\n                            }\n                        }\n                    }\n                }\n            }\n            catch (e) {\n                const m = field\n                    ? `cannot encode field ${type.typeName}.${field.name} to JSON`\n                    : `cannot encode message ${type.typeName} to JSON`;\n                const r = e instanceof Error ? e.message : String(e);\n                throw new Error(m + (r.length > 0 ? `: ${r}` : \"\"));\n            }\n            return json;\n        },\n        readScalar(type, json, longType) {\n            // The signature of our internal function has changed. For backwards-\n            // compatibility, we support the old form that is part of the public API\n            // through the interface JsonFormat.\n            return readScalar(type, json, longType !== null && longType !== void 0 ? longType : _scalar_js__WEBPACK_IMPORTED_MODULE_3__.LongType.BIGINT, true);\n        },\n        writeScalar(type, value, emitDefaultValues) {\n            // The signature of our internal function has changed. For backwards-\n            // compatibility, we support the old form that is part of the public API\n            // through the interface JsonFormat.\n            if (value === undefined) {\n                return undefined;\n            }\n            if (emitDefaultValues || (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.isScalarZeroValue)(type, value)) {\n                return writeScalar(type, value);\n            }\n            return undefined;\n        },\n        debug: debugJsonValue,\n    };\n}\nfunction debugJsonValue(json) {\n    if (json === null) {\n        return \"null\";\n    }\n    switch (typeof json) {\n        case \"object\":\n            return Array.isArray(json) ? \"array\" : \"object\";\n        case \"string\":\n            return json.length > 100 ? \"string\" : `\"${json.split('\"').join('\\\\\"')}\"`;\n        default:\n            return String(json);\n    }\n}\n// Read a JSON value for a field.\n// The \"parentType\" argument is only used to provide context in errors.\nfunction readField(target, jsonValue, field, options, parentType) {\n    let localName = field.localName;\n    if (field.repeated) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(field.kind != \"map\");\n        if (jsonValue === null) {\n            return;\n        }\n        if (!Array.isArray(jsonValue)) {\n            throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`);\n        }\n        const targetArray = target[localName];\n        for (const jsonItem of jsonValue) {\n            if (jsonItem === null) {\n                throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonItem)}`);\n            }\n            switch (field.kind) {\n                case \"message\":\n                    targetArray.push(field.T.fromJson(jsonItem, options));\n                    break;\n                case \"enum\":\n                    const enumValue = readEnum(field.T, jsonItem, options.ignoreUnknownFields, true);\n                    if (enumValue !== tokenIgnoredUnknownEnum) {\n                        targetArray.push(enumValue);\n                    }\n                    break;\n                case \"scalar\":\n                    try {\n                        targetArray.push(readScalar(field.T, jsonItem, field.L, true));\n                    }\n                    catch (e) {\n                        let m = `cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonItem)}`;\n                        if (e instanceof Error && e.message.length > 0) {\n                            m += `: ${e.message}`;\n                        }\n                        throw new Error(m);\n                    }\n                    break;\n            }\n        }\n    }\n    else if (field.kind == \"map\") {\n        if (jsonValue === null) {\n            return;\n        }\n        if (typeof jsonValue != \"object\" || Array.isArray(jsonValue)) {\n            throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`);\n        }\n        const targetMap = target[localName];\n        for (const [jsonMapKey, jsonMapValue] of Object.entries(jsonValue)) {\n            if (jsonMapValue === null) {\n                throw new Error(`cannot decode field ${parentType.typeName}.${field.name} from JSON: map value null`);\n            }\n            let key;\n            try {\n                key = readMapKey(field.K, jsonMapKey);\n            }\n            catch (e) {\n                let m = `cannot decode map key for field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                if (e instanceof Error && e.message.length > 0) {\n                    m += `: ${e.message}`;\n                }\n                throw new Error(m);\n            }\n            switch (field.V.kind) {\n                case \"message\":\n                    targetMap[key] = field.V.T.fromJson(jsonMapValue, options);\n                    break;\n                case \"enum\":\n                    const enumValue = readEnum(field.V.T, jsonMapValue, options.ignoreUnknownFields, true);\n                    if (enumValue !== tokenIgnoredUnknownEnum) {\n                        targetMap[key] = enumValue;\n                    }\n                    break;\n                case \"scalar\":\n                    try {\n                        targetMap[key] = readScalar(field.V.T, jsonMapValue, _scalar_js__WEBPACK_IMPORTED_MODULE_3__.LongType.BIGINT, true);\n                    }\n                    catch (e) {\n                        let m = `cannot decode map value for field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                        if (e instanceof Error && e.message.length > 0) {\n                            m += `: ${e.message}`;\n                        }\n                        throw new Error(m);\n                    }\n                    break;\n            }\n        }\n    }\n    else {\n        if (field.oneof) {\n            target = target[field.oneof.localName] = { case: localName };\n            localName = \"value\";\n        }\n        switch (field.kind) {\n            case \"message\":\n                const messageType = field.T;\n                if (jsonValue === null &&\n                    messageType.typeName != \"google.protobuf.Value\") {\n                    return;\n                }\n                let currentValue = target[localName];\n                if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_6__.isMessage)(currentValue)) {\n                    currentValue.fromJson(jsonValue, options);\n                }\n                else {\n                    target[localName] = currentValue = messageType.fromJson(jsonValue, options);\n                    if (messageType.fieldWrapper && !field.oneof) {\n                        target[localName] =\n                            messageType.fieldWrapper.unwrapField(currentValue);\n                    }\n                }\n                break;\n            case \"enum\":\n                const enumValue = readEnum(field.T, jsonValue, options.ignoreUnknownFields, false);\n                switch (enumValue) {\n                    case tokenNull:\n                        (0,_reflect_js__WEBPACK_IMPORTED_MODULE_2__.clearField)(field, target);\n                        break;\n                    case tokenIgnoredUnknownEnum:\n                        break;\n                    default:\n                        target[localName] = enumValue;\n                        break;\n                }\n                break;\n            case \"scalar\":\n                try {\n                    const scalarValue = readScalar(field.T, jsonValue, field.L, false);\n                    switch (scalarValue) {\n                        case tokenNull:\n                            (0,_reflect_js__WEBPACK_IMPORTED_MODULE_2__.clearField)(field, target);\n                            break;\n                        default:\n                            target[localName] = scalarValue;\n                            break;\n                    }\n                }\n                catch (e) {\n                    let m = `cannot decode field ${parentType.typeName}.${field.name} from JSON: ${debugJsonValue(jsonValue)}`;\n                    if (e instanceof Error && e.message.length > 0) {\n                        m += `: ${e.message}`;\n                    }\n                    throw new Error(m);\n                }\n                break;\n        }\n    }\n}\nfunction readMapKey(type, json) {\n    if (type === _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BOOL) {\n        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n        switch (json) {\n            case \"true\":\n                json = true;\n                break;\n            case \"false\":\n                json = false;\n                break;\n        }\n    }\n    return readScalar(type, json, _scalar_js__WEBPACK_IMPORTED_MODULE_3__.LongType.BIGINT, true).toString();\n}\nfunction readScalar(type, json, longType, nullAsZeroValue) {\n    if (json === null) {\n        if (nullAsZeroValue) {\n            return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_4__.scalarZeroValue)(type, longType);\n        }\n        return tokenNull;\n    }\n    // every valid case in the switch below returns, and every fall\n    // through is regarded as a failure.\n    switch (type) {\n        // float, double: JSON value will be a number or one of the special string values \"NaN\", \"Infinity\", and \"-Infinity\".\n        // Either numbers or strings are accepted. Exponent notation is also accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.DOUBLE:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FLOAT:\n            if (json === \"NaN\")\n                return Number.NaN;\n            if (json === \"Infinity\")\n                return Number.POSITIVE_INFINITY;\n            if (json === \"-Infinity\")\n                return Number.NEGATIVE_INFINITY;\n            if (json === \"\") {\n                // empty string is not a number\n                break;\n            }\n            if (typeof json == \"string\" && json.trim().length !== json.length) {\n                // extra whitespace\n                break;\n            }\n            if (typeof json != \"string\" && typeof json != \"number\") {\n                break;\n            }\n            const float = Number(json);\n            if (Number.isNaN(float)) {\n                // not a number\n                break;\n            }\n            if (!Number.isFinite(float)) {\n                // infinity and -infinity are handled by string representation above, so this is an error\n                break;\n            }\n            if (type == _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FLOAT)\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assertFloat32)(float);\n            return float;\n        // int32, fixed32, uint32: JSON value will be a decimal number. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT32:\n            let int32;\n            if (typeof json == \"number\")\n                int32 = json;\n            else if (typeof json == \"string\" && json.length > 0) {\n                if (json.trim().length === json.length)\n                    int32 = Number(json);\n            }\n            if (int32 === undefined)\n                break;\n            if (type == _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT32 || type == _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED32)\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assertUInt32)(int32);\n            else\n                (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assertInt32)(int32);\n            return int32;\n        // int64, fixed64, uint64: JSON value will be a decimal string. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT64:\n            if (typeof json != \"number\" && typeof json != \"string\")\n                break;\n            const long = _proto_int64_js__WEBPACK_IMPORTED_MODULE_7__.protoInt64.parse(json);\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            return longType ? long.toString() : long;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT64:\n            if (typeof json != \"number\" && typeof json != \"string\")\n                break;\n            const uLong = _proto_int64_js__WEBPACK_IMPORTED_MODULE_7__.protoInt64.uParse(json);\n            // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n            return longType ? uLong.toString() : uLong;\n        // bool:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BOOL:\n            if (typeof json !== \"boolean\")\n                break;\n            return json;\n        // string:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.STRING:\n            if (typeof json !== \"string\") {\n                break;\n            }\n            // A string must always contain UTF-8 encoded or 7-bit ASCII.\n            // We validate with encodeURIComponent, which appears to be the fastest widely available option.\n            try {\n                encodeURIComponent(json);\n            }\n            catch (e) {\n                throw new Error(\"invalid UTF8\");\n            }\n            return json;\n        // bytes: JSON value will be the data encoded as a string using standard base64 encoding with paddings.\n        // Either standard or URL-safe base64 encoding with/without paddings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BYTES:\n            if (json === \"\")\n                return new Uint8Array(0);\n            if (typeof json !== \"string\")\n                break;\n            return _proto_base64_js__WEBPACK_IMPORTED_MODULE_8__.protoBase64.dec(json);\n    }\n    throw new Error();\n}\nfunction readEnum(type, json, ignoreUnknownFields, nullAsZeroValue) {\n    if (json === null) {\n        if (type.typeName == \"google.protobuf.NullValue\") {\n            return 0; // google.protobuf.NullValue.NULL_VALUE = 0\n        }\n        return nullAsZeroValue ? type.values[0].no : tokenNull;\n    }\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n    switch (typeof json) {\n        case \"number\":\n            if (Number.isInteger(json)) {\n                return json;\n            }\n            break;\n        case \"string\":\n            const value = type.findName(json);\n            if (value !== undefined) {\n                return value.no;\n            }\n            if (ignoreUnknownFields) {\n                return tokenIgnoredUnknownEnum;\n            }\n            break;\n    }\n    throw new Error(`cannot decode enum ${type.typeName} from JSON: ${debugJsonValue(json)}`);\n}\n// Decide whether an unset field should be emitted with JSON write option `emitDefaultValues`\nfunction canEmitFieldDefaultValue(field) {\n    if (field.repeated || field.kind == \"map\") {\n        // maps are {}, repeated fields are []\n        return true;\n    }\n    if (field.oneof) {\n        // oneof fields are never emitted\n        return false;\n    }\n    if (field.kind == \"message\") {\n        // singular message field are allowed to emit JSON null, but we do not\n        return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions\n    if (field.opt || field.req) {\n        // the field uses explicit presence, so we cannot emit a zero value\n        return false;\n    }\n    return true;\n}\nfunction writeField(field, value, options) {\n    if (field.kind == \"map\") {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"object\" && value != null);\n        const jsonObj = {};\n        const entries = Object.entries(value);\n        switch (field.V.kind) {\n            case \"scalar\":\n                for (const [entryKey, entryValue] of entries) {\n                    jsonObj[entryKey.toString()] = writeScalar(field.V.T, entryValue); // JSON standard allows only (double quoted) string as property key\n                }\n                break;\n            case \"message\":\n                for (const [entryKey, entryValue] of entries) {\n                    // JSON standard allows only (double quoted) string as property key\n                    jsonObj[entryKey.toString()] = entryValue.toJson(options);\n                }\n                break;\n            case \"enum\":\n                const enumType = field.V.T;\n                for (const [entryKey, entryValue] of entries) {\n                    // JSON standard allows only (double quoted) string as property key\n                    jsonObj[entryKey.toString()] = writeEnum(enumType, entryValue, options.enumAsInteger);\n                }\n                break;\n        }\n        return options.emitDefaultValues || entries.length > 0\n            ? jsonObj\n            : undefined;\n    }\n    if (field.repeated) {\n        (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(Array.isArray(value));\n        const jsonArr = [];\n        switch (field.kind) {\n            case \"scalar\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(writeScalar(field.T, value[i]));\n                }\n                break;\n            case \"enum\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(writeEnum(field.T, value[i], options.enumAsInteger));\n                }\n                break;\n            case \"message\":\n                for (let i = 0; i < value.length; i++) {\n                    jsonArr.push(value[i].toJson(options));\n                }\n                break;\n        }\n        return options.emitDefaultValues || jsonArr.length > 0\n            ? jsonArr\n            : undefined;\n    }\n    switch (field.kind) {\n        case \"scalar\":\n            return writeScalar(field.T, value);\n        case \"enum\":\n            return writeEnum(field.T, value, options.enumAsInteger);\n        case \"message\":\n            return (0,_field_wrapper_js__WEBPACK_IMPORTED_MODULE_9__.wrapField)(field.T, value).toJson(options);\n    }\n}\nfunction writeEnum(type, value, enumAsInteger) {\n    var _a;\n    (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"number\");\n    if (type.typeName == \"google.protobuf.NullValue\") {\n        return null;\n    }\n    if (enumAsInteger) {\n        return value;\n    }\n    const val = type.findNumber(value);\n    return (_a = val === null || val === void 0 ? void 0 : val.name) !== null && _a !== void 0 ? _a : value; // if we don't know the enum value, just return the number\n}\nfunction writeScalar(type, value) {\n    switch (type) {\n        // int32, fixed32, uint32: JSON value will be a decimal number. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED32:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT32:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"number\");\n            return value;\n        // float, double: JSON value will be a number or one of the special string values \"NaN\", \"Infinity\", and \"-Infinity\".\n        // Either numbers or strings are accepted. Exponent notation is also accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FLOAT:\n        // assertFloat32(value);\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.DOUBLE: // eslint-disable-line no-fallthrough\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"number\");\n            if (Number.isNaN(value))\n                return \"NaN\";\n            if (value === Number.POSITIVE_INFINITY)\n                return \"Infinity\";\n            if (value === Number.NEGATIVE_INFINITY)\n                return \"-Infinity\";\n            return value;\n        // string:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.STRING:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"string\");\n            return value;\n        // bool:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BOOL:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"boolean\");\n            return value;\n        // JSON value will be a decimal string. Either numbers or strings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.UINT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.SINT64:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(typeof value == \"bigint\" ||\n                typeof value == \"string\" ||\n                typeof value == \"number\");\n            return value.toString();\n        // bytes: JSON value will be the data encoded as a string using standard base64 encoding with paddings.\n        // Either standard or URL-safe base64 encoding with/without paddings are accepted.\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_3__.ScalarType.BYTES:\n            (0,_assert_js__WEBPACK_IMPORTED_MODULE_5__.assert)(value instanceof Uint8Array);\n            return _proto_base64_js__WEBPACK_IMPORTED_MODULE_8__.protoBase64.enc(value);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeMessageType: () => (/* binding */ makeMessageType)\n/* harmony export */ });\n/* harmony import */ var _message_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Create a new message type using the given runtime.\n */\nfunction makeMessageType(runtime, typeName, fields, opt) {\n    var _a;\n    const localName = (_a = opt === null || opt === void 0 ? void 0 : opt.localName) !== null && _a !== void 0 ? _a : typeName.substring(typeName.lastIndexOf(\".\") + 1);\n    const type = {\n        [localName]: function (data) {\n            runtime.util.initFields(this);\n            runtime.util.initPartial(data, this);\n        },\n    }[localName];\n    Object.setPrototypeOf(type.prototype, new _message_js__WEBPACK_IMPORTED_MODULE_0__.Message());\n    Object.assign(type, {\n        runtime,\n        typeName,\n        fields: runtime.util.newFieldList(fields),\n        fromBinary(bytes, options) {\n            return new type().fromBinary(bytes, options);\n        },\n        fromJson(jsonValue, options) {\n            return new type().fromJson(jsonValue, options);\n        },\n        fromJsonString(jsonString, options) {\n            return new type().fromJsonString(jsonString, options);\n        },\n        equals(a, b) {\n            return runtime.util.equals(type, a, b);\n        },\n    });\n    return type;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/names.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fieldJsonName: () => (/* binding */ fieldJsonName),\n/* harmony export */   findEnumSharedPrefix: () => (/* binding */ findEnumSharedPrefix),\n/* harmony export */   localFieldName: () => (/* binding */ localFieldName),\n/* harmony export */   localName: () => (/* binding */ localName),\n/* harmony export */   localOneofName: () => (/* binding */ localOneofName),\n/* harmony export */   safeIdentifier: () => (/* binding */ safeIdentifier),\n/* harmony export */   safeObjectProperty: () => (/* binding */ safeObjectProperty)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Returns the name of a protobuf element in generated code.\n *\n * Field names - including oneofs - are converted to lowerCamelCase. For\n * messages, enumerations and services, the package name is stripped from\n * the type name. For nested messages and enumerations, the names are joined\n * with an underscore. For methods, the first character is made lowercase.\n */\nfunction localName(desc) {\n    switch (desc.kind) {\n        case \"field\":\n            return localFieldName(desc.name, desc.oneof !== undefined);\n        case \"oneof\":\n            return localOneofName(desc.name);\n        case \"enum\":\n        case \"message\":\n        case \"service\":\n        case \"extension\": {\n            const pkg = desc.file.proto.package;\n            const offset = pkg === undefined ? 0 : pkg.length + 1;\n            const name = desc.typeName.substring(offset).replace(/\\./g, \"_\");\n            // For services, we only care about safe identifiers, not safe object properties,\n            // but we have shipped v1 with a bug that respected object properties, and we\n            // do not want to introduce a breaking change, so we continue to escape for\n            // safe object properties.\n            // See https://github.com/bufbuild/protobuf-es/pull/391\n            return safeObjectProperty(safeIdentifier(name));\n        }\n        case \"enum_value\": {\n            let name = desc.name;\n            const sharedPrefix = desc.parent.sharedPrefix;\n            if (sharedPrefix !== undefined) {\n                name = name.substring(sharedPrefix.length);\n            }\n            return safeObjectProperty(name);\n        }\n        case \"rpc\": {\n            let name = desc.name;\n            if (name.length == 0) {\n                return name;\n            }\n            name = name[0].toLowerCase() + name.substring(1);\n            return safeObjectProperty(name);\n        }\n    }\n}\n/**\n * Returns the name of a field in generated code.\n */\nfunction localFieldName(protoName, inOneof) {\n    const name = protoCamelCase(protoName);\n    if (inOneof) {\n        // oneof member names are not properties, but values of the `case` property.\n        return name;\n    }\n    return safeObjectProperty(safeMessageProperty(name));\n}\n/**\n * Returns the name of a oneof group in generated code.\n */\nfunction localOneofName(protoName) {\n    return localFieldName(protoName, false);\n}\n/**\n * Returns the JSON name for a protobuf field, exactly like protoc does.\n */\nconst fieldJsonName = protoCamelCase;\n/**\n * Finds a prefix shared by enum values, for example `MY_ENUM_` for\n * `enum MyEnum {MY_ENUM_A=0; MY_ENUM_B=1;}`.\n */\nfunction findEnumSharedPrefix(enumName, valueNames) {\n    const prefix = camelToSnakeCase(enumName) + \"_\";\n    for (const name of valueNames) {\n        if (!name.toLowerCase().startsWith(prefix)) {\n            return undefined;\n        }\n        const shortName = name.substring(prefix.length);\n        if (shortName.length == 0) {\n            return undefined;\n        }\n        if (/^\\d/.test(shortName)) {\n            // identifiers must not start with numbers\n            return undefined;\n        }\n    }\n    return prefix;\n}\n/**\n * Converts lowerCamelCase or UpperCamelCase into lower_snake_case.\n * This is used to find shared prefixes in an enum.\n */\nfunction camelToSnakeCase(camel) {\n    return (camel.substring(0, 1) + camel.substring(1).replace(/[A-Z]/g, (c) => \"_\" + c)).toLowerCase();\n}\n/**\n * Converts snake_case to protoCamelCase according to the convention\n * used by protoc to convert a field name to a JSON name.\n */\nfunction protoCamelCase(snakeCase) {\n    let capNext = false;\n    const b = [];\n    for (let i = 0; i < snakeCase.length; i++) {\n        let c = snakeCase.charAt(i);\n        switch (c) {\n            case \"_\":\n                capNext = true;\n                break;\n            case \"0\":\n            case \"1\":\n            case \"2\":\n            case \"3\":\n            case \"4\":\n            case \"5\":\n            case \"6\":\n            case \"7\":\n            case \"8\":\n            case \"9\":\n                b.push(c);\n                capNext = false;\n                break;\n            default:\n                if (capNext) {\n                    capNext = false;\n                    c = c.toUpperCase();\n                }\n                b.push(c);\n                break;\n        }\n    }\n    return b.join(\"\");\n}\n/**\n * Names that cannot be used for identifiers, such as class names,\n * but _can_ be used for object properties.\n */\nconst reservedIdentifiers = new Set([\n    // ECMAScript 2015 keywords\n    \"break\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"debugger\",\n    \"default\",\n    \"delete\",\n    \"do\",\n    \"else\",\n    \"export\",\n    \"extends\",\n    \"false\",\n    \"finally\",\n    \"for\",\n    \"function\",\n    \"if\",\n    \"import\",\n    \"in\",\n    \"instanceof\",\n    \"new\",\n    \"null\",\n    \"return\",\n    \"super\",\n    \"switch\",\n    \"this\",\n    \"throw\",\n    \"true\",\n    \"try\",\n    \"typeof\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // ECMAScript 2015 future reserved keywords\n    \"enum\",\n    \"implements\",\n    \"interface\",\n    \"let\",\n    \"package\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"static\",\n    // Class name cannot be 'Object' when targeting ES5 with module CommonJS\n    \"Object\",\n    // TypeScript keywords that cannot be used for types (as opposed to variables)\n    \"bigint\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    // Identifiers reserved for the runtime, so we can generate legible code\n    \"globalThis\",\n    \"Uint8Array\",\n    \"Partial\",\n]);\n/**\n * Names that cannot be used for object properties because they are reserved\n * by built-in JavaScript properties.\n */\nconst reservedObjectProperties = new Set([\n    // names reserved by JavaScript\n    \"constructor\",\n    \"toString\",\n    \"toJSON\",\n    \"valueOf\",\n]);\n/**\n * Names that cannot be used for object properties because they are reserved\n * by the runtime.\n */\nconst reservedMessageProperties = new Set([\n    // names reserved by the runtime\n    \"getType\",\n    \"clone\",\n    \"equals\",\n    \"fromBinary\",\n    \"fromJson\",\n    \"fromJsonString\",\n    \"toBinary\",\n    \"toJson\",\n    \"toJsonString\",\n    // names reserved by the runtime for the future\n    \"toObject\",\n]);\nconst fallback = (name) => `${name}$`;\n/**\n * Will wrap names that are Object prototype properties or names reserved\n * for `Message`s.\n */\nconst safeMessageProperty = (name) => {\n    if (reservedMessageProperties.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n/**\n * Names that cannot be used for object properties because they are reserved\n * by built-in JavaScript properties.\n */\nconst safeObjectProperty = (name) => {\n    if (reservedObjectProperties.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n/**\n * Names that can be used for identifiers or class properties\n */\nconst safeIdentifier = (name) => {\n    if (reservedIdentifiers.has(name)) {\n        return fallback(name);\n    }\n    return name;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/names.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeProtoRuntime: () => (/* binding */ makeProtoRuntime)\n/* harmony export */ });\n/* harmony import */ var _enum_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./enum.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js\");\n/* harmony import */ var _message_type_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./message-type.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/message-type.js\");\n/* harmony import */ var _extensions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./extensions.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/extensions.js\");\n/* harmony import */ var _json_format_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./json-format.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/json-format.js\");\n/* harmony import */ var _binary_format_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary-format.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/binary-format.js\");\n/* harmony import */ var _util_common_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util-common.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n\nfunction makeProtoRuntime(syntax, newFieldList, initFields) {\n    return {\n        syntax,\n        json: (0,_json_format_js__WEBPACK_IMPORTED_MODULE_0__.makeJsonFormat)(),\n        bin: (0,_binary_format_js__WEBPACK_IMPORTED_MODULE_1__.makeBinaryFormat)(),\n        util: Object.assign(Object.assign({}, (0,_util_common_js__WEBPACK_IMPORTED_MODULE_2__.makeUtilCommon)()), { newFieldList,\n            initFields }),\n        makeMessageType(typeName, fields, opt) {\n            return (0,_message_type_js__WEBPACK_IMPORTED_MODULE_3__.makeMessageType)(this, typeName, fields, opt);\n        },\n        makeEnum: _enum_js__WEBPACK_IMPORTED_MODULE_4__.makeEnum,\n        makeEnumType: _enum_js__WEBPACK_IMPORTED_MODULE_4__.makeEnumType,\n        getEnumType: _enum_js__WEBPACK_IMPORTED_MODULE_4__.getEnumType,\n        makeExtension(typeName, extendee, field) {\n            return (0,_extensions_js__WEBPACK_IMPORTED_MODULE_5__.makeExtension)(this, typeName, extendee, field);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearField: () => (/* binding */ clearField),\n/* harmony export */   isFieldSet: () => (/* binding */ isFieldSet)\n/* harmony export */ });\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./scalars.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n/**\n * Returns true if the field is set.\n */\nfunction isFieldSet(field, target) {\n    const localName = field.localName;\n    if (field.repeated) {\n        return target[localName].length > 0;\n    }\n    if (field.oneof) {\n        return target[field.oneof.localName].case === localName; // eslint-disable-line @typescript-eslint/no-unsafe-member-access\n    }\n    switch (field.kind) {\n        case \"enum\":\n        case \"scalar\":\n            if (field.opt || field.req) {\n                // explicit presence\n                return target[localName] !== undefined;\n            }\n            // implicit presence\n            if (field.kind == \"enum\") {\n                return target[localName] !== field.T.values[0].no;\n            }\n            return !(0,_scalars_js__WEBPACK_IMPORTED_MODULE_0__.isScalarZeroValue)(field.T, target[localName]);\n        case \"message\":\n            return target[localName] !== undefined;\n        case \"map\":\n            return Object.keys(target[localName]).length > 0; // eslint-disable-line @typescript-eslint/no-unsafe-argument\n    }\n}\n/**\n * Resets the field, so that isFieldSet() will return false.\n */\nfunction clearField(field, target) {\n    const localName = field.localName;\n    const implicitPresence = !field.opt && !field.req;\n    if (field.repeated) {\n        target[localName] = [];\n    }\n    else if (field.oneof) {\n        target[field.oneof.localName] = { case: undefined };\n    }\n    else {\n        switch (field.kind) {\n            case \"map\":\n                target[localName] = {};\n                break;\n            case \"enum\":\n                target[localName] = implicitPresence ? field.T.values[0].no : undefined;\n                break;\n            case \"scalar\":\n                target[localName] = implicitPresence\n                    ? (0,_scalars_js__WEBPACK_IMPORTED_MODULE_0__.scalarZeroValue)(field.T, field.L)\n                    : undefined;\n                break;\n            case \"message\":\n                target[localName] = undefined;\n                break;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/reflect.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isScalarZeroValue: () => (/* binding */ isScalarZeroValue),\n/* harmony export */   scalarEquals: () => (/* binding */ scalarEquals),\n/* harmony export */   scalarZeroValue: () => (/* binding */ scalarZeroValue)\n/* harmony export */ });\n/* harmony import */ var _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../proto-int64.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../scalar.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n/**\n * Returns true if both scalar values are equal.\n */\nfunction scalarEquals(type, a, b) {\n    if (a === b) {\n        // This correctly matches equal values except BYTES and (possibly) 64-bit integers.\n        return true;\n    }\n    // Special case BYTES - we need to compare each byte individually\n    if (type == _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BYTES) {\n        if (!(a instanceof Uint8Array) || !(b instanceof Uint8Array)) {\n            return false;\n        }\n        if (a.length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < a.length; i++) {\n            if (a[i] !== b[i]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    // Special case 64-bit integers - we support number, string and bigint representation.\n    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.UINT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SINT64:\n            // Loose comparison will match between 0n, 0 and \"0\".\n            return a == b;\n    }\n    // Anything that hasn't been caught by strict comparison or special cased\n    // BYTES and 64-bit integers is not equal.\n    return false;\n}\n/**\n * Returns the zero value for the given scalar type.\n */\nfunction scalarZeroValue(type, longType) {\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BOOL:\n            return false;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.UINT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.FIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.INT64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SFIXED64:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.SINT64:\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison -- acceptable since it's covered by tests\n            return (longType == 0 ? _proto_int64_js__WEBPACK_IMPORTED_MODULE_1__.protoInt64.zero : \"0\");\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.DOUBLE:\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.FLOAT:\n            return 0.0;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BYTES:\n            return new Uint8Array(0);\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.STRING:\n            return \"\";\n        default:\n            // Handles INT32, UINT32, SINT32, FIXED32, SFIXED32.\n            // We do not use individual cases to save a few bytes code size.\n            return 0;\n    }\n}\n/**\n * Returns true for a zero-value. For example, an integer has the zero-value `0`,\n * a boolean is `false`, a string is `\"\"`, and bytes is an empty Uint8Array.\n *\n * In proto3, zero-values are not written to the wire, unless the field is\n * optional or repeated.\n */\nfunction isScalarZeroValue(type, value) {\n    switch (type) {\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BOOL:\n            return value === false;\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.STRING:\n            return value === \"\";\n        case _scalar_js__WEBPACK_IMPORTED_MODULE_0__.ScalarType.BYTES:\n            return value instanceof Uint8Array && !value.byteLength;\n        default:\n            return value == 0; // Loose comparison matches 0n, 0 and \"0\"\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeUtilCommon: () => (/* binding */ makeUtilCommon)\n/* harmony export */ });\n/* harmony import */ var _enum_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./enum.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/enum.js\");\n/* harmony import */ var _scalars_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./scalars.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _scalar_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../scalar.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\");\n/* harmony import */ var _is_message_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../is-message.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/is-message.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n\n/* eslint-disable @typescript-eslint/no-explicit-any,@typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-return,@typescript-eslint/no-unsafe-argument,no-case-declarations */\nfunction makeUtilCommon() {\n    return {\n        setEnumType: _enum_js__WEBPACK_IMPORTED_MODULE_0__.setEnumType,\n        initPartial(source, target) {\n            if (source === undefined) {\n                return;\n            }\n            const type = target.getType();\n            for (const member of type.fields.byMember()) {\n                const localName = member.localName, t = target, s = source;\n                if (s[localName] == null) {\n                    // TODO if source is a Message instance, we should use isFieldSet() here to support future field presence\n                    continue;\n                }\n                switch (member.kind) {\n                    case \"oneof\":\n                        const sk = s[localName].case;\n                        if (sk === undefined) {\n                            continue;\n                        }\n                        const sourceField = member.findField(sk);\n                        let val = s[localName].value;\n                        if (sourceField &&\n                            sourceField.kind == \"message\" &&\n                            !(0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(val, sourceField.T)) {\n                            val = new sourceField.T(val);\n                        }\n                        else if (sourceField &&\n                            sourceField.kind === \"scalar\" &&\n                            sourceField.T === _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES) {\n                            val = toU8Arr(val);\n                        }\n                        t[localName] = { case: sk, value: val };\n                        break;\n                    case \"scalar\":\n                    case \"enum\":\n                        let copy = s[localName];\n                        if (member.T === _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES) {\n                            copy = member.repeated\n                                ? copy.map(toU8Arr)\n                                : toU8Arr(copy);\n                        }\n                        t[localName] = copy;\n                        break;\n                    case \"map\":\n                        switch (member.V.kind) {\n                            case \"scalar\":\n                            case \"enum\":\n                                if (member.V.T === _scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.BYTES) {\n                                    for (const [k, v] of Object.entries(s[localName])) {\n                                        t[localName][k] = toU8Arr(v);\n                                    }\n                                }\n                                else {\n                                    Object.assign(t[localName], s[localName]);\n                                }\n                                break;\n                            case \"message\":\n                                const messageType = member.V.T;\n                                for (const k of Object.keys(s[localName])) {\n                                    let val = s[localName][k];\n                                    if (!messageType.fieldWrapper) {\n                                        // We only take partial input for messages that are not a wrapper type.\n                                        // For those messages, we recursively normalize the partial input.\n                                        val = new messageType(val);\n                                    }\n                                    t[localName][k] = val;\n                                }\n                                break;\n                        }\n                        break;\n                    case \"message\":\n                        const mt = member.T;\n                        if (member.repeated) {\n                            t[localName] = s[localName].map((val) => (0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(val, mt) ? val : new mt(val));\n                        }\n                        else {\n                            const val = s[localName];\n                            if (mt.fieldWrapper) {\n                                if (\n                                // We can't use BytesValue.typeName as that will create a circular import\n                                mt.typeName === \"google.protobuf.BytesValue\") {\n                                    t[localName] = toU8Arr(val);\n                                }\n                                else {\n                                    t[localName] = val;\n                                }\n                            }\n                            else {\n                                t[localName] = (0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(val, mt) ? val : new mt(val);\n                            }\n                        }\n                        break;\n                }\n            }\n        },\n        // TODO use isFieldSet() here to support future field presence\n        equals(type, a, b) {\n            if (a === b) {\n                return true;\n            }\n            if (!a || !b) {\n                return false;\n            }\n            return type.fields.byMember().every((m) => {\n                const va = a[m.localName];\n                const vb = b[m.localName];\n                if (m.repeated) {\n                    if (va.length !== vb.length) {\n                        return false;\n                    }\n                    // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- repeated fields are never \"map\"\n                    switch (m.kind) {\n                        case \"message\":\n                            return va.every((a, i) => m.T.equals(a, vb[i]));\n                        case \"scalar\":\n                            return va.every((a, i) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(m.T, a, vb[i]));\n                        case \"enum\":\n                            return va.every((a, i) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, a, vb[i]));\n                    }\n                    throw new Error(`repeated cannot contain ${m.kind}`);\n                }\n                switch (m.kind) {\n                    case \"message\":\n                        let a = va;\n                        let b = vb;\n                        if (m.T.fieldWrapper) {\n                            if (a !== undefined && !(0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(a)) {\n                                a = m.T.fieldWrapper.wrapField(a);\n                            }\n                            if (b !== undefined && !(0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(b)) {\n                                b = m.T.fieldWrapper.wrapField(b);\n                            }\n                        }\n                        return m.T.equals(a, b);\n                    case \"enum\":\n                        return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, va, vb);\n                    case \"scalar\":\n                        return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(m.T, va, vb);\n                    case \"oneof\":\n                        if (va.case !== vb.case) {\n                            return false;\n                        }\n                        const s = m.findField(va.case);\n                        if (s === undefined) {\n                            return true;\n                        }\n                        // eslint-disable-next-line @typescript-eslint/switch-exhaustiveness-check -- oneof fields are never \"map\"\n                        switch (s.kind) {\n                            case \"message\":\n                                return s.T.equals(va.value, vb.value);\n                            case \"enum\":\n                                return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, va.value, vb.value);\n                            case \"scalar\":\n                                return (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(s.T, va.value, vb.value);\n                        }\n                        throw new Error(`oneof cannot contain ${s.kind}`);\n                    case \"map\":\n                        const keys = Object.keys(va).concat(Object.keys(vb));\n                        switch (m.V.kind) {\n                            case \"message\":\n                                const messageType = m.V.T;\n                                return keys.every((k) => messageType.equals(va[k], vb[k]));\n                            case \"enum\":\n                                return keys.every((k) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(_scalar_js__WEBPACK_IMPORTED_MODULE_2__.ScalarType.INT32, va[k], vb[k]));\n                            case \"scalar\":\n                                const scalarType = m.V.T;\n                                return keys.every((k) => (0,_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarEquals)(scalarType, va[k], vb[k]));\n                        }\n                        break;\n                }\n            });\n        },\n        // TODO use isFieldSet() here to support future field presence\n        clone(message) {\n            const type = message.getType(), target = new type(), any = target;\n            for (const member of type.fields.byMember()) {\n                const source = message[member.localName];\n                let copy;\n                if (member.repeated) {\n                    copy = source.map(cloneSingularField);\n                }\n                else if (member.kind == \"map\") {\n                    copy = any[member.localName];\n                    for (const [key, v] of Object.entries(source)) {\n                        copy[key] = cloneSingularField(v);\n                    }\n                }\n                else if (member.kind == \"oneof\") {\n                    const f = member.findField(source.case);\n                    copy = f\n                        ? { case: source.case, value: cloneSingularField(source.value) }\n                        : { case: undefined };\n                }\n                else {\n                    copy = cloneSingularField(source);\n                }\n                any[member.localName] = copy;\n            }\n            for (const uf of type.runtime.bin.listUnknownFields(message)) {\n                type.runtime.bin.onUnknownField(any, uf.no, uf.wireType, uf.data);\n            }\n            return target;\n        },\n    };\n}\n// clone a single field value - i.e. the element type of repeated fields, the value type of maps\nfunction cloneSingularField(value) {\n    if (value === undefined) {\n        return value;\n    }\n    if ((0,_is_message_js__WEBPACK_IMPORTED_MODULE_1__.isMessage)(value)) {\n        return value.clone();\n    }\n    if (value instanceof Uint8Array) {\n        const c = new Uint8Array(value.byteLength);\n        c.set(value);\n        return c;\n    }\n    return value;\n}\n// converts any ArrayLike<number> to Uint8Array if necessary.\nfunction toU8Arr(input) {\n    return input instanceof Uint8Array ? input : new Uint8Array(input);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL3ByaXZhdGUvdXRpbC1jb21tb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUN3QztBQUNBO0FBQ0k7QUFDRjtBQUNHO0FBQzdDO0FBQ087QUFDUDtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIseURBQVM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsa0RBQVU7QUFDeEQ7QUFDQTtBQUNBLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxrREFBVTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxrREFBVTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFFQUFxRSx5REFBUztBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLHlEQUFTO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCx5REFBWTtBQUNsRTtBQUNBLHNEQUFzRCx5REFBWSxDQUFDLGtEQUFVO0FBQzdFO0FBQ0EsK0RBQStELE9BQU87QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELHlEQUFTO0FBQzdEO0FBQ0E7QUFDQSxvREFBb0QseURBQVM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix5REFBWSxDQUFDLGtEQUFVO0FBQ3REO0FBQ0EsK0JBQStCLHlEQUFZO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLHlEQUFZLENBQUMsa0RBQVU7QUFDOUQ7QUFDQSx1Q0FBdUMseURBQVk7QUFDbkQ7QUFDQSxnRUFBZ0UsT0FBTztBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RCx5REFBWSxDQUFDLGtEQUFVO0FBQ2hGO0FBQ0E7QUFDQSx5REFBeUQseURBQVk7QUFDckU7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUIsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5REFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ZvaWNlLWFzc2lzdGFudDIvLi9ub2RlX21vZHVsZXMvQGJ1ZmJ1aWxkL3Byb3RvYnVmL2Rpc3QvZXNtL3ByaXZhdGUvdXRpbC1jb21tb24uanM/OWMyNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgMjAyMS0yMDI0IEJ1ZiBUZWNobm9sb2dpZXMsIEluYy5cbi8vXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuaW1wb3J0IHsgc2V0RW51bVR5cGUgfSBmcm9tIFwiLi9lbnVtLmpzXCI7XG5pbXBvcnQgeyBNZXNzYWdlIH0gZnJvbSBcIi4uL21lc3NhZ2UuanNcIjtcbmltcG9ydCB7IHNjYWxhckVxdWFscyB9IGZyb20gXCIuL3NjYWxhcnMuanNcIjtcbmltcG9ydCB7IFNjYWxhclR5cGUgfSBmcm9tIFwiLi4vc2NhbGFyLmpzXCI7XG5pbXBvcnQgeyBpc01lc3NhZ2UgfSBmcm9tIFwiLi4vaXMtbWVzc2FnZS5qc1wiO1xuLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueSxAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5zYWZlLWFzc2lnbm1lbnQsQHR5cGVzY3JpcHQtZXNsaW50L25vLXVuc2FmZS1tZW1iZXItYWNjZXNzLEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtcmV0dXJuLEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtYXJndW1lbnQsbm8tY2FzZS1kZWNsYXJhdGlvbnMgKi9cbmV4cG9ydCBmdW5jdGlvbiBtYWtlVXRpbENvbW1vbigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBzZXRFbnVtVHlwZSxcbiAgICAgICAgaW5pdFBhcnRpYWwoc291cmNlLCB0YXJnZXQpIHtcbiAgICAgICAgICAgIGlmIChzb3VyY2UgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IHR5cGUgPSB0YXJnZXQuZ2V0VHlwZSgpO1xuICAgICAgICAgICAgZm9yIChjb25zdCBtZW1iZXIgb2YgdHlwZS5maWVsZHMuYnlNZW1iZXIoKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGxvY2FsTmFtZSA9IG1lbWJlci5sb2NhbE5hbWUsIHQgPSB0YXJnZXQsIHMgPSBzb3VyY2U7XG4gICAgICAgICAgICAgICAgaWYgKHNbbG9jYWxOYW1lXSA9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE8gaWYgc291cmNlIGlzIGEgTWVzc2FnZSBpbnN0YW5jZSwgd2Ugc2hvdWxkIHVzZSBpc0ZpZWxkU2V0KCkgaGVyZSB0byBzdXBwb3J0IGZ1dHVyZSBmaWVsZCBwcmVzZW5jZVxuICAgICAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgc3dpdGNoIChtZW1iZXIua2luZCkge1xuICAgICAgICAgICAgICAgICAgICBjYXNlIFwib25lb2ZcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHNrID0gc1tsb2NhbE5hbWVdLmNhc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2sgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc291cmNlRmllbGQgPSBtZW1iZXIuZmluZEZpZWxkKHNrKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCB2YWwgPSBzW2xvY2FsTmFtZV0udmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc291cmNlRmllbGQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3VyY2VGaWVsZC5raW5kID09IFwibWVzc2FnZVwiICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgIWlzTWVzc2FnZSh2YWwsIHNvdXJjZUZpZWxkLlQpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsID0gbmV3IHNvdXJjZUZpZWxkLlQodmFsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKHNvdXJjZUZpZWxkICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc291cmNlRmllbGQua2luZCA9PT0gXCJzY2FsYXJcIiAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvdXJjZUZpZWxkLlQgPT09IFNjYWxhclR5cGUuQllURVMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWwgPSB0b1U4QXJyKHZhbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0W2xvY2FsTmFtZV0gPSB7IGNhc2U6IHNrLCB2YWx1ZTogdmFsIH07XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBcInNjYWxhclwiOlxuICAgICAgICAgICAgICAgICAgICBjYXNlIFwiZW51bVwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGNvcHkgPSBzW2xvY2FsTmFtZV07XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAobWVtYmVyLlQgPT09IFNjYWxhclR5cGUuQllURVMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb3B5ID0gbWVtYmVyLnJlcGVhdGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gY29weS5tYXAodG9VOEFycilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB0b1U4QXJyKGNvcHkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgdFtsb2NhbE5hbWVdID0gY29weTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICBjYXNlIFwibWFwXCI6XG4gICAgICAgICAgICAgICAgICAgICAgICBzd2l0Y2ggKG1lbWJlci5WLmtpbmQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIFwic2NhbGFyXCI6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSBcImVudW1cIjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG1lbWJlci5WLlQgPT09IFNjYWxhclR5cGUuQllURVMpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgW2ssIHZdIG9mIE9iamVjdC5lbnRyaWVzKHNbbG9jYWxOYW1lXSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0W2xvY2FsTmFtZV1ba10gPSB0b1U4QXJyKHYpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmFzc2lnbih0W2xvY2FsTmFtZV0sIHNbbG9jYWxOYW1lXSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSBcIm1lc3NhZ2VcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVzc2FnZVR5cGUgPSBtZW1iZXIuVi5UO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGsgb2YgT2JqZWN0LmtleXMoc1tsb2NhbE5hbWVdKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHZhbCA9IHNbbG9jYWxOYW1lXVtrXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghbWVzc2FnZVR5cGUuZmllbGRXcmFwcGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2Ugb25seSB0YWtlIHBhcnRpYWwgaW5wdXQgZm9yIG1lc3NhZ2VzIHRoYXQgYXJlIG5vdCBhIHdyYXBwZXIgdHlwZS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBGb3IgdGhvc2UgbWVzc2FnZXMsIHdlIHJlY3Vyc2l2ZWx5IG5vcm1hbGl6ZSB0aGUgcGFydGlhbCBpbnB1dC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWwgPSBuZXcgbWVzc2FnZVR5cGUodmFsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRbbG9jYWxOYW1lXVtrXSA9IHZhbDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICBjYXNlIFwibWVzc2FnZVwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbXQgPSBtZW1iZXIuVDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtZW1iZXIucmVwZWF0ZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0W2xvY2FsTmFtZV0gPSBzW2xvY2FsTmFtZV0ubWFwKCh2YWwpID0+IGlzTWVzc2FnZSh2YWwsIG10KSA/IHZhbCA6IG5ldyBtdCh2YWwpKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHZhbCA9IHNbbG9jYWxOYW1lXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobXQuZmllbGRXcmFwcGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gV2UgY2FuJ3QgdXNlIEJ5dGVzVmFsdWUudHlwZU5hbWUgYXMgdGhhdCB3aWxsIGNyZWF0ZSBhIGNpcmN1bGFyIGltcG9ydFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtdC50eXBlTmFtZSA9PT0gXCJnb29nbGUucHJvdG9idWYuQnl0ZXNWYWx1ZVwiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0W2xvY2FsTmFtZV0gPSB0b1U4QXJyKHZhbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0W2xvY2FsTmFtZV0gPSB2YWw7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRbbG9jYWxOYW1lXSA9IGlzTWVzc2FnZSh2YWwsIG10KSA/IHZhbCA6IG5ldyBtdCh2YWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICAgICAgLy8gVE9ETyB1c2UgaXNGaWVsZFNldCgpIGhlcmUgdG8gc3VwcG9ydCBmdXR1cmUgZmllbGQgcHJlc2VuY2VcbiAgICAgICAgZXF1YWxzKHR5cGUsIGEsIGIpIHtcbiAgICAgICAgICAgIGlmIChhID09PSBiKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWEgfHwgIWIpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdHlwZS5maWVsZHMuYnlNZW1iZXIoKS5ldmVyeSgobSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IHZhID0gYVttLmxvY2FsTmFtZV07XG4gICAgICAgICAgICAgICAgY29uc3QgdmIgPSBiW20ubG9jYWxOYW1lXTtcbiAgICAgICAgICAgICAgICBpZiAobS5yZXBlYXRlZCkge1xuICAgICAgICAgICAgICAgICAgICBpZiAodmEubGVuZ3RoICE9PSB2Yi5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3N3aXRjaC1leGhhdXN0aXZlbmVzcy1jaGVjayAtLSByZXBlYXRlZCBmaWVsZHMgYXJlIG5ldmVyIFwibWFwXCJcbiAgICAgICAgICAgICAgICAgICAgc3dpdGNoIChtLmtpbmQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgXCJtZXNzYWdlXCI6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhLmV2ZXJ5KChhLCBpKSA9PiBtLlQuZXF1YWxzKGEsIHZiW2ldKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjYXNlIFwic2NhbGFyXCI6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhLmV2ZXJ5KChhLCBpKSA9PiBzY2FsYXJFcXVhbHMobS5ULCBhLCB2YltpXSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSBcImVudW1cIjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmEuZXZlcnkoKGEsIGkpID0+IHNjYWxhckVxdWFscyhTY2FsYXJUeXBlLklOVDMyLCBhLCB2YltpXSkpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgcmVwZWF0ZWQgY2Fubm90IGNvbnRhaW4gJHttLmtpbmR9YCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHN3aXRjaCAobS5raW5kKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgXCJtZXNzYWdlXCI6XG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgYSA9IHZhO1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IGIgPSB2YjtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChtLlQuZmllbGRXcmFwcGVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGEgIT09IHVuZGVmaW5lZCAmJiAhaXNNZXNzYWdlKGEpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGEgPSBtLlQuZmllbGRXcmFwcGVyLndyYXBGaWVsZChhKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGIgIT09IHVuZGVmaW5lZCAmJiAhaXNNZXNzYWdlKGIpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGIgPSBtLlQuZmllbGRXcmFwcGVyLndyYXBGaWVsZChiKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gbS5ULmVxdWFscyhhLCBiKTtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBcImVudW1cIjpcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBzY2FsYXJFcXVhbHMoU2NhbGFyVHlwZS5JTlQzMiwgdmEsIHZiKTtcbiAgICAgICAgICAgICAgICAgICAgY2FzZSBcInNjYWxhclwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHNjYWxhckVxdWFscyhtLlQsIHZhLCB2Yik7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgXCJvbmVvZlwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHZhLmNhc2UgIT09IHZiLmNhc2UpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBzID0gbS5maW5kRmllbGQodmEuY2FzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocyA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3N3aXRjaC1leGhhdXN0aXZlbmVzcy1jaGVjayAtLSBvbmVvZiBmaWVsZHMgYXJlIG5ldmVyIFwibWFwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN3aXRjaCAocy5raW5kKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSBcIm1lc3NhZ2VcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHMuVC5lcXVhbHModmEudmFsdWUsIHZiLnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIFwiZW51bVwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2NhbGFyRXF1YWxzKFNjYWxhclR5cGUuSU5UMzIsIHZhLnZhbHVlLCB2Yi52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSBcInNjYWxhclwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gc2NhbGFyRXF1YWxzKHMuVCwgdmEudmFsdWUsIHZiLnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgb25lb2YgY2Fubm90IGNvbnRhaW4gJHtzLmtpbmR9YCk7XG4gICAgICAgICAgICAgICAgICAgIGNhc2UgXCJtYXBcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGtleXMgPSBPYmplY3Qua2V5cyh2YSkuY29uY2F0KE9iamVjdC5rZXlzKHZiKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBzd2l0Y2ggKG0uVi5raW5kKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSBcIm1lc3NhZ2VcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbWVzc2FnZVR5cGUgPSBtLlYuVDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGtleXMuZXZlcnkoKGspID0+IG1lc3NhZ2VUeXBlLmVxdWFscyh2YVtrXSwgdmJba10pKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIFwiZW51bVwiOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ga2V5cy5ldmVyeSgoaykgPT4gc2NhbGFyRXF1YWxzKFNjYWxhclR5cGUuSU5UMzIsIHZhW2tdLCB2YltrXSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgXCJzY2FsYXJcIjpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgc2NhbGFyVHlwZSA9IG0uVi5UO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4ga2V5cy5ldmVyeSgoaykgPT4gc2NhbGFyRXF1YWxzKHNjYWxhclR5cGUsIHZhW2tdLCB2YltrXSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIC8vIFRPRE8gdXNlIGlzRmllbGRTZXQoKSBoZXJlIHRvIHN1cHBvcnQgZnV0dXJlIGZpZWxkIHByZXNlbmNlXG4gICAgICAgIGNsb25lKG1lc3NhZ2UpIHtcbiAgICAgICAgICAgIGNvbnN0IHR5cGUgPSBtZXNzYWdlLmdldFR5cGUoKSwgdGFyZ2V0ID0gbmV3IHR5cGUoKSwgYW55ID0gdGFyZ2V0O1xuICAgICAgICAgICAgZm9yIChjb25zdCBtZW1iZXIgb2YgdHlwZS5maWVsZHMuYnlNZW1iZXIoKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHNvdXJjZSA9IG1lc3NhZ2VbbWVtYmVyLmxvY2FsTmFtZV07XG4gICAgICAgICAgICAgICAgbGV0IGNvcHk7XG4gICAgICAgICAgICAgICAgaWYgKG1lbWJlci5yZXBlYXRlZCkge1xuICAgICAgICAgICAgICAgICAgICBjb3B5ID0gc291cmNlLm1hcChjbG9uZVNpbmd1bGFyRmllbGQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChtZW1iZXIua2luZCA9PSBcIm1hcFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvcHkgPSBhbnlbbWVtYmVyLmxvY2FsTmFtZV07XG4gICAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgW2tleSwgdl0gb2YgT2JqZWN0LmVudHJpZXMoc291cmNlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29weVtrZXldID0gY2xvbmVTaW5ndWxhckZpZWxkKHYpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKG1lbWJlci5raW5kID09IFwib25lb2ZcIikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBmID0gbWVtYmVyLmZpbmRGaWVsZChzb3VyY2UuY2FzZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvcHkgPSBmXG4gICAgICAgICAgICAgICAgICAgICAgICA/IHsgY2FzZTogc291cmNlLmNhc2UsIHZhbHVlOiBjbG9uZVNpbmd1bGFyRmllbGQoc291cmNlLnZhbHVlKSB9XG4gICAgICAgICAgICAgICAgICAgICAgICA6IHsgY2FzZTogdW5kZWZpbmVkIH07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb3B5ID0gY2xvbmVTaW5ndWxhckZpZWxkKHNvdXJjZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGFueVttZW1iZXIubG9jYWxOYW1lXSA9IGNvcHk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBmb3IgKGNvbnN0IHVmIG9mIHR5cGUucnVudGltZS5iaW4ubGlzdFVua25vd25GaWVsZHMobWVzc2FnZSkpIHtcbiAgICAgICAgICAgICAgICB0eXBlLnJ1bnRpbWUuYmluLm9uVW5rbm93bkZpZWxkKGFueSwgdWYubm8sIHVmLndpcmVUeXBlLCB1Zi5kYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB0YXJnZXQ7XG4gICAgICAgIH0sXG4gICAgfTtcbn1cbi8vIGNsb25lIGEgc2luZ2xlIGZpZWxkIHZhbHVlIC0gaS5lLiB0aGUgZWxlbWVudCB0eXBlIG9mIHJlcGVhdGVkIGZpZWxkcywgdGhlIHZhbHVlIHR5cGUgb2YgbWFwc1xuZnVuY3Rpb24gY2xvbmVTaW5ndWxhckZpZWxkKHZhbHVlKSB7XG4gICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIHZhbHVlO1xuICAgIH1cbiAgICBpZiAoaXNNZXNzYWdlKHZhbHVlKSkge1xuICAgICAgICByZXR1cm4gdmFsdWUuY2xvbmUoKTtcbiAgICB9XG4gICAgaWYgKHZhbHVlIGluc3RhbmNlb2YgVWludDhBcnJheSkge1xuICAgICAgICBjb25zdCBjID0gbmV3IFVpbnQ4QXJyYXkodmFsdWUuYnl0ZUxlbmd0aCk7XG4gICAgICAgIGMuc2V0KHZhbHVlKTtcbiAgICAgICAgcmV0dXJuIGM7XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn1cbi8vIGNvbnZlcnRzIGFueSBBcnJheUxpa2U8bnVtYmVyPiB0byBVaW50OEFycmF5IGlmIG5lY2Vzc2FyeS5cbmZ1bmN0aW9uIHRvVThBcnIoaW5wdXQpIHtcbiAgICByZXR1cm4gaW5wdXQgaW5zdGFuY2VvZiBVaW50OEFycmF5ID8gaW5wdXQgOiBuZXcgVWludDhBcnJheShpbnB1dCk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/util-common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js":
/*!******************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   protoBase64: () => (/* binding */ protoBase64)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/* eslint-disable @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unnecessary-condition, prefer-const */\n// lookup table from base64 character to byte\nlet encTable = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\".split(\"\");\n// lookup table from base64 character *code* to byte because lookup by number is fast\nlet decTable = [];\nfor (let i = 0; i < encTable.length; i++)\n    decTable[encTable[i].charCodeAt(0)] = i;\n// support base64url variants\ndecTable[\"-\".charCodeAt(0)] = encTable.indexOf(\"+\");\ndecTable[\"_\".charCodeAt(0)] = encTable.indexOf(\"/\");\nconst protoBase64 = {\n    /**\n     * Decodes a base64 string to a byte array.\n     *\n     * - ignores white-space, including line breaks and tabs\n     * - allows inner padding (can decode concatenated base64 strings)\n     * - does not require padding\n     * - understands base64url encoding:\n     *   \"-\" instead of \"+\",\n     *   \"_\" instead of \"/\",\n     *   no padding\n     */\n    dec(base64Str) {\n        // estimate byte size, not accounting for inner padding and whitespace\n        let es = (base64Str.length * 3) / 4;\n        if (base64Str[base64Str.length - 2] == \"=\")\n            es -= 2;\n        else if (base64Str[base64Str.length - 1] == \"=\")\n            es -= 1;\n        let bytes = new Uint8Array(es), bytePos = 0, // position in byte array\n        groupPos = 0, // position in base64 group\n        b, // current byte\n        p = 0; // previous byte\n        for (let i = 0; i < base64Str.length; i++) {\n            b = decTable[base64Str.charCodeAt(i)];\n            if (b === undefined) {\n                switch (base64Str[i]) {\n                    // @ts-ignore TS7029: Fallthrough case in switch\n                    case \"=\":\n                        groupPos = 0; // reset state when padding found\n                    // @ts-ignore TS7029: Fallthrough case in switch\n                    case \"\\n\":\n                    case \"\\r\":\n                    case \"\\t\":\n                    case \" \":\n                        continue; // skip white-space, and padding\n                    default:\n                        throw Error(\"invalid base64 string.\");\n                }\n            }\n            switch (groupPos) {\n                case 0:\n                    p = b;\n                    groupPos = 1;\n                    break;\n                case 1:\n                    bytes[bytePos++] = (p << 2) | ((b & 48) >> 4);\n                    p = b;\n                    groupPos = 2;\n                    break;\n                case 2:\n                    bytes[bytePos++] = ((p & 15) << 4) | ((b & 60) >> 2);\n                    p = b;\n                    groupPos = 3;\n                    break;\n                case 3:\n                    bytes[bytePos++] = ((p & 3) << 6) | b;\n                    groupPos = 0;\n                    break;\n            }\n        }\n        if (groupPos == 1)\n            throw Error(\"invalid base64 string.\");\n        return bytes.subarray(0, bytePos);\n    },\n    /**\n     * Encode a byte array to a base64 string.\n     */\n    enc(bytes) {\n        let base64 = \"\", groupPos = 0, // position in base64 group\n        b, // current byte\n        p = 0; // carry over from previous byte\n        for (let i = 0; i < bytes.length; i++) {\n            b = bytes[i];\n            switch (groupPos) {\n                case 0:\n                    base64 += encTable[b >> 2];\n                    p = (b & 3) << 4;\n                    groupPos = 1;\n                    break;\n                case 1:\n                    base64 += encTable[p | (b >> 4)];\n                    p = (b & 15) << 2;\n                    groupPos = 2;\n                    break;\n                case 2:\n                    base64 += encTable[p | (b >> 6)];\n                    base64 += encTable[b & 63];\n                    groupPos = 0;\n                    break;\n            }\n        }\n        // add output padding\n        if (groupPos) {\n            base64 += encTable[p];\n            base64 += \"=\";\n            if (groupPos == 1)\n                base64 += \"=\";\n        }\n        return base64;\n    },\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-base64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   protoInt64: () => (/* binding */ protoInt64)\n/* harmony export */ });\n/* harmony import */ var _private_assert_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/assert.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/assert.js\");\n/* harmony import */ var _google_varint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./google/varint.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/varint.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\nfunction makeInt64Support() {\n    const dv = new DataView(new ArrayBuffer(8));\n    // note that Safari 14 implements BigInt, but not the DataView methods\n    const ok = typeof BigInt === \"function\" &&\n        typeof dv.getBigInt64 === \"function\" &&\n        typeof dv.getBigUint64 === \"function\" &&\n        typeof dv.setBigInt64 === \"function\" &&\n        typeof dv.setBigUint64 === \"function\" &&\n        (typeof process != \"object\" ||\n            typeof process.env != \"object\" ||\n            process.env.BUF_BIGINT_DISABLE !== \"1\");\n    if (ok) {\n        const MIN = BigInt(\"-9223372036854775808\"), MAX = BigInt(\"9223372036854775807\"), UMIN = BigInt(\"0\"), UMAX = BigInt(\"18446744073709551615\");\n        return {\n            zero: BigInt(0),\n            supported: true,\n            parse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > MAX || bi < MIN) {\n                    throw new Error(`int64 invalid: ${value}`);\n                }\n                return bi;\n            },\n            uParse(value) {\n                const bi = typeof value == \"bigint\" ? value : BigInt(value);\n                if (bi > UMAX || bi < UMIN) {\n                    throw new Error(`uint64 invalid: ${value}`);\n                }\n                return bi;\n            },\n            enc(value) {\n                dv.setBigInt64(0, this.parse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            uEnc(value) {\n                dv.setBigInt64(0, this.uParse(value), true);\n                return {\n                    lo: dv.getInt32(0, true),\n                    hi: dv.getInt32(4, true),\n                };\n            },\n            dec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigInt64(0, true);\n            },\n            uDec(lo, hi) {\n                dv.setInt32(0, lo, true);\n                dv.setInt32(4, hi, true);\n                return dv.getBigUint64(0, true);\n            },\n        };\n    }\n    const assertInt64String = (value) => (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assert)(/^-?[0-9]+$/.test(value), `int64 invalid: ${value}`);\n    const assertUInt64String = (value) => (0,_private_assert_js__WEBPACK_IMPORTED_MODULE_0__.assert)(/^[0-9]+$/.test(value), `uint64 invalid: ${value}`);\n    return {\n        zero: \"0\",\n        supported: false,\n        parse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return value;\n        },\n        uParse(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return value;\n        },\n        enc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertInt64String(value);\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.int64FromString)(value);\n        },\n        uEnc(value) {\n            if (typeof value != \"string\") {\n                value = value.toString();\n            }\n            assertUInt64String(value);\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.int64FromString)(value);\n        },\n        dec(lo, hi) {\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.int64ToString)(lo, hi);\n        },\n        uDec(lo, hi) {\n            return (0,_google_varint_js__WEBPACK_IMPORTED_MODULE_1__.uInt64ToString)(lo, hi);\n        },\n    };\n}\nconst protoInt64 = makeInt64Support();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto-int64.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js":
/*!************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/proto3.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   proto3: () => (/* binding */ proto3)\n/* harmony export */ });\n/* harmony import */ var _private_proto_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./private/proto-runtime.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/proto-runtime.js\");\n/* harmony import */ var _private_field_list_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./private/field-list.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-list.js\");\n/* harmony import */ var _private_scalars_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./private/scalars.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/scalars.js\");\n/* harmony import */ var _private_field_normalize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./private/field-normalize.js */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/private/field-normalize.js\");\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n\n\n\n\n/**\n * Provides functionality for messages defined with the proto3 syntax.\n */\nconst proto3 = (0,_private_proto_runtime_js__WEBPACK_IMPORTED_MODULE_0__.makeProtoRuntime)(\"proto3\", (fields) => {\n    return new _private_field_list_js__WEBPACK_IMPORTED_MODULE_1__.InternalFieldList(fields, (source) => (0,_private_field_normalize_js__WEBPACK_IMPORTED_MODULE_2__.normalizeFieldInfos)(source, true));\n}, \n// TODO merge with proto2 and initExtensionField, also see initPartial, equals, clone\n(target) => {\n    for (const member of target.getType().fields.byMember()) {\n        if (member.opt) {\n            continue;\n        }\n        const name = member.localName, t = target;\n        if (member.repeated) {\n            t[name] = [];\n            continue;\n        }\n        switch (member.kind) {\n            case \"oneof\":\n                t[name] = { case: undefined };\n                break;\n            case \"enum\":\n                t[name] = 0;\n                break;\n            case \"map\":\n                t[name] = {};\n                break;\n            case \"scalar\":\n                t[name] = (0,_private_scalars_js__WEBPACK_IMPORTED_MODULE_3__.scalarZeroValue)(member.T, member.L);\n                break;\n            case \"message\":\n                // message fields are always optional in proto3\n                break;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/proto3.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js":
/*!************************************************************!*\
  !*** ./node_modules/@bufbuild/protobuf/dist/esm/scalar.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LongType: () => (/* binding */ LongType),\n/* harmony export */   ScalarType: () => (/* binding */ ScalarType)\n/* harmony export */ });\n// Copyright 2021-2024 Buf Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//      http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\n/**\n * Scalar value types. This is a subset of field types declared by protobuf\n * enum google.protobuf.FieldDescriptorProto.Type The types GROUP and MESSAGE\n * are omitted, but the numerical values are identical.\n */\nvar ScalarType;\n(function (ScalarType) {\n    // 0 is reserved for errors.\n    // Order is weird for historical reasons.\n    ScalarType[ScalarType[\"DOUBLE\"] = 1] = \"DOUBLE\";\n    ScalarType[ScalarType[\"FLOAT\"] = 2] = \"FLOAT\";\n    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT64 if\n    // negative values are likely.\n    ScalarType[ScalarType[\"INT64\"] = 3] = \"INT64\";\n    ScalarType[ScalarType[\"UINT64\"] = 4] = \"UINT64\";\n    // Not ZigZag encoded.  Negative numbers take 10 bytes.  Use TYPE_SINT32 if\n    // negative values are likely.\n    ScalarType[ScalarType[\"INT32\"] = 5] = \"INT32\";\n    ScalarType[ScalarType[\"FIXED64\"] = 6] = \"FIXED64\";\n    ScalarType[ScalarType[\"FIXED32\"] = 7] = \"FIXED32\";\n    ScalarType[ScalarType[\"BOOL\"] = 8] = \"BOOL\";\n    ScalarType[ScalarType[\"STRING\"] = 9] = \"STRING\";\n    // Tag-delimited aggregate.\n    // Group type is deprecated and not supported in proto3. However, Proto3\n    // implementations should still be able to parse the group wire format and\n    // treat group fields as unknown fields.\n    // TYPE_GROUP = 10,\n    // TYPE_MESSAGE = 11,  // Length-delimited aggregate.\n    // New in version 2.\n    ScalarType[ScalarType[\"BYTES\"] = 12] = \"BYTES\";\n    ScalarType[ScalarType[\"UINT32\"] = 13] = \"UINT32\";\n    // TYPE_ENUM = 14,\n    ScalarType[ScalarType[\"SFIXED32\"] = 15] = \"SFIXED32\";\n    ScalarType[ScalarType[\"SFIXED64\"] = 16] = \"SFIXED64\";\n    ScalarType[ScalarType[\"SINT32\"] = 17] = \"SINT32\";\n    ScalarType[ScalarType[\"SINT64\"] = 18] = \"SINT64\";\n})(ScalarType || (ScalarType = {}));\n/**\n * JavaScript representation of fields with 64 bit integral types (int64, uint64,\n * sint64, fixed64, sfixed64).\n *\n * This is a subset of google.protobuf.FieldOptions.JSType, which defines JS_NORMAL,\n * JS_STRING, and JS_NUMBER. Protobuf-ES uses BigInt by default, but will use\n * String if `[jstype = JS_STRING]` is specified.\n *\n * ```protobuf\n * uint64 field_a = 1; // BigInt\n * uint64 field_b = 2 [jstype = JS_NORMAL]; // BigInt\n * uint64 field_b = 2 [jstype = JS_NUMBER]; // BigInt\n * uint64 field_b = 2 [jstype = JS_STRING]; // String\n * ```\n */\nvar LongType;\n(function (LongType) {\n    /**\n     * Use JavaScript BigInt.\n     */\n    LongType[LongType[\"BIGINT\"] = 0] = \"BIGINT\";\n    /**\n     * Use JavaScript String.\n     *\n     * Field option `[jstype = JS_STRING]`.\n     */\n    LongType[LongType[\"STRING\"] = 1] = \"STRING\";\n})(LongType || (LongType = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/scalar.js\n");

/***/ })

};
;