"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/camelcase";
exports.ids = ["vendor-chunks/camelcase"];
exports.modules = {

/***/ "(rsc)/./node_modules/camelcase/index.js":
/*!*****************************************!*\
  !*** ./node_modules/camelcase/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ camelCase)\n/* harmony export */ });\nconst UPPERCASE = /[\\p{Lu}]/u;\nconst LOWERCASE = /[\\p{Ll}]/u;\nconst LEADING_CAPITAL = /^[\\p{Lu}](?![\\p{Lu}])/gu;\nconst IDENTIFIER = /([\\p{Alpha}\\p{N}_]|$)/u;\nconst SEPARATORS = /[_.\\- ]+/;\n\nconst LEADING_SEPARATORS = new RegExp('^' + SEPARATORS.source);\nconst SEPARATORS_AND_IDENTIFIER = new RegExp(SEPARATORS.source + IDENTIFIER.source, 'gu');\nconst NUMBERS_AND_IDENTIFIER = new RegExp('\\\\d+' + IDENTIFIER.source, 'gu');\n\nconst preserveCamelCase = (string, toLowerCase, toUpperCase, preserveConsecutiveUppercase) => {\n\tlet isLastCharLower = false;\n\tlet isLastCharUpper = false;\n\tlet isLastLastCharUpper = false;\n\tlet isLastLastCharPreserved = false;\n\n\tfor (let index = 0; index < string.length; index++) {\n\t\tconst character = string[index];\n\t\tisLastLastCharPreserved = index > 2 ? string[index - 3] === '-' : true;\n\n\t\tif (isLastCharLower && UPPERCASE.test(character)) {\n\t\t\tstring = string.slice(0, index) + '-' + string.slice(index);\n\t\t\tisLastCharLower = false;\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = true;\n\t\t\tindex++;\n\t\t} else if (isLastCharUpper && isLastLastCharUpper && LOWERCASE.test(character) && (!isLastLastCharPreserved || preserveConsecutiveUppercase)) {\n\t\t\tstring = string.slice(0, index - 1) + '-' + string.slice(index - 1);\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = false;\n\t\t\tisLastCharLower = true;\n\t\t} else {\n\t\t\tisLastCharLower = toLowerCase(character) === character && toUpperCase(character) !== character;\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = toUpperCase(character) === character && toLowerCase(character) !== character;\n\t\t}\n\t}\n\n\treturn string;\n};\n\nconst preserveConsecutiveUppercase = (input, toLowerCase) => {\n\tLEADING_CAPITAL.lastIndex = 0;\n\n\treturn input.replaceAll(LEADING_CAPITAL, match => toLowerCase(match));\n};\n\nconst postProcess = (input, toUpperCase) => {\n\tSEPARATORS_AND_IDENTIFIER.lastIndex = 0;\n\tNUMBERS_AND_IDENTIFIER.lastIndex = 0;\n\n\treturn input\n\t\t.replaceAll(NUMBERS_AND_IDENTIFIER, (match, pattern, offset) => ['_', '-'].includes(input.charAt(offset + match.length)) ? match : toUpperCase(match))\n\t\t.replaceAll(SEPARATORS_AND_IDENTIFIER, (_, identifier) => toUpperCase(identifier));\n};\n\nfunction camelCase(input, options) {\n\tif (!(typeof input === 'string' || Array.isArray(input))) {\n\t\tthrow new TypeError('Expected the input to be `string | string[]`');\n\t}\n\n\toptions = {\n\t\tpascalCase: false,\n\t\tpreserveConsecutiveUppercase: false,\n\t\t...options,\n\t};\n\n\tif (Array.isArray(input)) {\n\t\tinput = input.map(x => x.trim())\n\t\t\t.filter(x => x.length)\n\t\t\t.join('-');\n\t} else {\n\t\tinput = input.trim();\n\t}\n\n\tif (input.length === 0) {\n\t\treturn '';\n\t}\n\n\tconst toLowerCase = options.locale === false\n\t\t? string => string.toLowerCase()\n\t\t: string => string.toLocaleLowerCase(options.locale);\n\n\tconst toUpperCase = options.locale === false\n\t\t? string => string.toUpperCase()\n\t\t: string => string.toLocaleUpperCase(options.locale);\n\n\tif (input.length === 1) {\n\t\tif (SEPARATORS.test(input)) {\n\t\t\treturn '';\n\t\t}\n\n\t\treturn options.pascalCase ? toUpperCase(input) : toLowerCase(input);\n\t}\n\n\tconst hasUpperCase = input !== toLowerCase(input);\n\n\tif (hasUpperCase) {\n\t\tinput = preserveCamelCase(input, toLowerCase, toUpperCase, options.preserveConsecutiveUppercase);\n\t}\n\n\tinput = input.replace(LEADING_SEPARATORS, '');\n\tinput = options.preserveConsecutiveUppercase ? preserveConsecutiveUppercase(input, toLowerCase) : toLowerCase(input);\n\n\tif (options.pascalCase) {\n\t\tinput = toUpperCase(input.charAt(0)) + input.slice(1);\n\t}\n\n\treturn postProcess(input, toUpperCase);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/camelcase/index.js\n");

/***/ })

};
;