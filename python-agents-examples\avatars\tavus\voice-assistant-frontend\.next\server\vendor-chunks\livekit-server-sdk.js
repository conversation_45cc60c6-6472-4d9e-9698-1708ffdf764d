"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/livekit-server-sdk";
exports.ids = ["vendor-chunks/livekit-server-sdk"];
exports.modules = {

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/AccessToken.js":
/*!*************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/AccessToken.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessToken: () => (/* binding */ AccessToken),\n/* harmony export */   TokenVerifier: () => (/* binding */ TokenVerifier)\n/* harmony export */ });\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/sign.js\");\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jose */ \"(rsc)/./node_modules/jose/dist/node/esm/jwt/verify.js\");\n/* harmony import */ var _grants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./grants.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/grants.js\");\n\n\nconst defaultTTL = `6h`;\nconst defaultClockToleranceSeconds = 10;\nclass AccessToken {\n  /**\n   * Creates a new AccessToken\n   * @param apiKey - API Key, can be set in env LIVEKIT_API_KEY\n   * @param apiSecret - Secret, can be set in env LIVEKIT_API_SECRET\n   */\n  constructor(apiKey, apiSecret, options) {\n    if (!apiKey) {\n      apiKey = process.env.LIVEKIT_API_KEY;\n    }\n    if (!apiSecret) {\n      apiSecret = process.env.LIVEKIT_API_SECRET;\n    }\n    if (!apiKey || !apiSecret) {\n      throw Error(\"api-key and api-secret must be set\");\n    } else if (typeof document !== \"undefined\") {\n      console.error(\n        \"You should not include your API secret in your web client bundle.\\n\\nYour web client should request a token from your backend server which should then use the API secret to generate a token. See https://docs.livekit.io/client/connect/\"\n      );\n    }\n    this.apiKey = apiKey;\n    this.apiSecret = apiSecret;\n    this.grants = {};\n    this.identity = options == null ? void 0 : options.identity;\n    this.ttl = (options == null ? void 0 : options.ttl) || defaultTTL;\n    if (typeof this.ttl === \"number\") {\n      this.ttl = `${this.ttl}s`;\n    }\n    if (options == null ? void 0 : options.metadata) {\n      this.metadata = options.metadata;\n    }\n    if (options == null ? void 0 : options.attributes) {\n      this.attributes = options.attributes;\n    }\n    if (options == null ? void 0 : options.name) {\n      this.name = options.name;\n    }\n  }\n  /**\n   * Adds a video grant to this token.\n   * @param grant -\n   */\n  addGrant(grant) {\n    this.grants.video = { ...this.grants.video ?? {}, ...grant };\n  }\n  /**\n   * Adds a SIP grant to this token.\n   * @param grant -\n   */\n  addSIPGrant(grant) {\n    this.grants.sip = { ...this.grants.sip ?? {}, ...grant };\n  }\n  get name() {\n    return this.grants.name;\n  }\n  set name(name) {\n    this.grants.name = name;\n  }\n  get metadata() {\n    return this.grants.metadata;\n  }\n  /**\n   * Set metadata to be passed to the Participant, used only when joining the room\n   */\n  set metadata(md) {\n    this.grants.metadata = md;\n  }\n  get attributes() {\n    return this.grants.attributes;\n  }\n  set attributes(attrs) {\n    this.grants.attributes = attrs;\n  }\n  get kind() {\n    return this.grants.kind;\n  }\n  set kind(kind) {\n    this.grants.kind = kind;\n  }\n  get sha256() {\n    return this.grants.sha256;\n  }\n  set sha256(sha) {\n    this.grants.sha256 = sha;\n  }\n  get roomPreset() {\n    return this.grants.roomPreset;\n  }\n  set roomPreset(preset) {\n    this.grants.roomPreset = preset;\n  }\n  get roomConfig() {\n    return this.grants.roomConfig;\n  }\n  set roomConfig(config) {\n    this.grants.roomConfig = config;\n  }\n  /**\n   * @returns JWT encoded token\n   */\n  async toJwt() {\n    var _a;\n    const secret = new TextEncoder().encode(this.apiSecret);\n    const jwt = new jose__WEBPACK_IMPORTED_MODULE_1__.SignJWT((0,_grants_js__WEBPACK_IMPORTED_MODULE_0__.claimsToJwtPayload)(this.grants)).setProtectedHeader({ alg: \"HS256\" }).setIssuer(this.apiKey).setExpirationTime(this.ttl).setNotBefore(0);\n    if (this.identity) {\n      jwt.setSubject(this.identity);\n    } else if ((_a = this.grants.video) == null ? void 0 : _a.roomJoin) {\n      throw Error(\"identity is required for join but not set\");\n    }\n    return jwt.sign(secret);\n  }\n}\nclass TokenVerifier {\n  constructor(apiKey, apiSecret) {\n    this.apiKey = apiKey;\n    this.apiSecret = apiSecret;\n  }\n  async verify(token, clockTolerance = defaultClockToleranceSeconds) {\n    const secret = new TextEncoder().encode(this.apiSecret);\n    const { payload } = await jose__WEBPACK_IMPORTED_MODULE_2__.jwtVerify(token, secret, {\n      issuer: this.apiKey,\n      clockTolerance\n    });\n    if (!payload) {\n      throw Error(\"invalid token\");\n    }\n    return payload;\n  }\n}\n\n//# sourceMappingURL=AccessToken.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/AccessToken.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/AgentDispatchClient.js":
/*!*********************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/AgentDispatchClient.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AgentDispatchClient: () => (/* binding */ AgentDispatchClient)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ServiceBase.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js\");\n/* harmony import */ var _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TwirpRPC.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js\");\n\n\n\nconst svc = \"AgentDispatchService\";\nclass AgentDispatchClient extends _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__.ServiceBase {\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host, apiKey, secret) {\n    super(apiKey, secret);\n    this.rpc = new _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.TwirpRpc(host, _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.livekitPackage);\n  }\n  /**\n   * Create an explicit dispatch for an agent to join a room. To use explicit\n   * dispatch, your agent must be registered with an `agentName`.\n   * @param roomName - name of the room to dispatch to\n   * @param agentName - name of the agent to dispatch\n   * @param options - optional metadata to send along with the dispatch\n   * @returns the dispatch that was created\n   */\n  async createDispatch(roomName, agentName, options) {\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateAgentDispatchRequest({\n      room: roomName,\n      agentName,\n      metadata: options == null ? void 0 : options.metadata\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateDispatch\",\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.AgentDispatch.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * Delete an explicit dispatch for an agent in a room.\n   * @param dispatchId - id of the dispatch to delete\n   * @param roomName - name of the room the dispatch is for\n   */\n  async deleteDispatch(dispatchId, roomName) {\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DeleteAgentDispatchRequest({\n      dispatchId,\n      room: roomName\n    }).toJson();\n    await this.rpc.request(\n      svc,\n      \"DeleteDispatch\",\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName })\n    );\n  }\n  /**\n   * Get an Agent dispatch by ID\n   * @param dispatchId - id of the dispatch to get\n   * @param roomName - name of the room the dispatch is for\n   * @returns the dispatch that was found, or undefined if not found\n   */\n  async getDispatch(dispatchId, roomName) {\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListAgentDispatchRequest({\n      dispatchId,\n      room: roomName\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"ListDispatch\",\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName })\n    );\n    const res = _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListAgentDispatchResponse.fromJson(data, { ignoreUnknownFields: true });\n    if (res.agentDispatches.length === 0) {\n      return void 0;\n    }\n    return res.agentDispatches[0];\n  }\n  /**\n   * List all agent dispatches for a room\n   * @param roomName - name of the room to list dispatches for\n   * @returns the list of dispatches\n   */\n  async listDispatch(roomName) {\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListAgentDispatchRequest({\n      room: roomName\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"ListDispatch\",\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName })\n    );\n    const res = _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListAgentDispatchResponse.fromJson(data, { ignoreUnknownFields: true });\n    return res.agentDispatches;\n  }\n}\n\n//# sourceMappingURL=AgentDispatchClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/AgentDispatchClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/EgressClient.js":
/*!**************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/EgressClient.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EgressClient: () => (/* binding */ EgressClient)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ServiceBase.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js\");\n/* harmony import */ var _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TwirpRPC.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js\");\n\n\n\nconst svc = \"Egress\";\nclass EgressClient extends _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__.ServiceBase {\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host, apiKey, secret) {\n    super(apiKey, secret);\n    this.rpc = new _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.TwirpRpc(host, _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.livekitPackage);\n  }\n  async startRoomCompositeEgress(roomName, output, optsOrLayout, options, audioOnly, videoOnly, customBaseUrl) {\n    let layout;\n    if (optsOrLayout !== void 0) {\n      if (typeof optsOrLayout === \"string\") {\n        layout = optsOrLayout;\n      } else {\n        const opts = optsOrLayout;\n        layout = opts.layout;\n        options = opts.encodingOptions;\n        audioOnly = opts.audioOnly;\n        videoOnly = opts.videoOnly;\n        customBaseUrl = opts.customBaseUrl;\n      }\n    }\n    layout ??= \"\";\n    audioOnly ??= false;\n    videoOnly ??= false;\n    customBaseUrl ??= \"\";\n    const {\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    } = this.getOutputParams(output, options);\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.RoomCompositeEgressRequest({\n      roomName,\n      layout,\n      audioOnly,\n      videoOnly,\n      customBaseUrl,\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"StartRoomCompositeEgress\",\n      req,\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param url - url\n   * @param output - file or stream output\n   * @param opts - WebOptions\n   */\n  async startWebEgress(url, output, opts) {\n    const audioOnly = (opts == null ? void 0 : opts.audioOnly) || false;\n    const videoOnly = (opts == null ? void 0 : opts.videoOnly) || false;\n    const awaitStartSignal = (opts == null ? void 0 : opts.awaitStartSignal) || false;\n    const {\n      output: legacyOutput,\n      options,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    } = this.getOutputParams(output, opts == null ? void 0 : opts.encodingOptions);\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.WebEgressRequest({\n      url,\n      audioOnly,\n      videoOnly,\n      awaitStartSignal,\n      output: legacyOutput,\n      options,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"StartWebEgress\",\n      req,\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * Export a participant's audio and video tracks,\n   *\n   * @param roomName - room name\n   * @param output - one or more outputs\n   * @param opts - ParticipantEgressOptions\n   */\n  async startParticipantEgress(roomName, identity, output, opts) {\n    const { options, fileOutputs, streamOutputs, segmentOutputs, imageOutputs } = this.getOutputParams(output, opts == null ? void 0 : opts.encodingOptions);\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantEgressRequest({\n      roomName,\n      identity,\n      screenShare: (opts == null ? void 0 : opts.screenShare) ?? false,\n      options,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"StartParticipantEgress\",\n      req,\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  async startTrackCompositeEgress(roomName, output, optsOrAudioTrackId, videoTrackId, options) {\n    let audioTrackId;\n    if (optsOrAudioTrackId !== void 0) {\n      if (typeof optsOrAudioTrackId === \"string\") {\n        audioTrackId = optsOrAudioTrackId;\n      } else {\n        const opts = optsOrAudioTrackId;\n        audioTrackId = opts.audioTrackId;\n        videoTrackId = opts.videoTrackId;\n        options = opts.encodingOptions;\n      }\n    }\n    audioTrackId ??= \"\";\n    videoTrackId ??= \"\";\n    const {\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    } = this.getOutputParams(output, options);\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackCompositeEgressRequest({\n      roomName,\n      audioTrackId,\n      videoTrackId,\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"StartTrackCompositeEgress\",\n      req,\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  isEncodedOutputs(output) {\n    return output.file !== void 0 || output.stream !== void 0 || output.segments !== void 0 || output.images !== void 0;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  isEncodedFileOutput(output) {\n    return output.filepath !== void 0 || output.fileType !== void 0;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  isSegmentedFileOutput(output) {\n    return output.filenamePrefix !== void 0 || output.playlistName !== void 0 || output.filenameSuffix !== void 0;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  isStreamOutput(output) {\n    return output.protocol !== void 0 || output.urls !== void 0;\n  }\n  getOutputParams(output, opts) {\n    let file;\n    let fileOutputs;\n    let stream;\n    let streamOutputs;\n    let segments;\n    let segmentOutputs;\n    let imageOutputs;\n    if (this.isEncodedOutputs(output)) {\n      if (output.file !== void 0) {\n        fileOutputs = [output.file];\n      }\n      if (output.stream !== void 0) {\n        streamOutputs = [output.stream];\n      }\n      if (output.segments !== void 0) {\n        segmentOutputs = [output.segments];\n      }\n      if (output.images !== void 0) {\n        imageOutputs = [output.images];\n      }\n    } else if (this.isEncodedFileOutput(output)) {\n      file = output;\n      fileOutputs = [file];\n    } else if (this.isSegmentedFileOutput(output)) {\n      segments = output;\n      segmentOutputs = [segments];\n    } else if (this.isStreamOutput(output)) {\n      stream = output;\n      streamOutputs = [stream];\n    }\n    let legacyOutput;\n    if (file) {\n      legacyOutput = {\n        case: \"file\",\n        value: file\n      };\n    } else if (stream) {\n      legacyOutput = {\n        case: \"stream\",\n        value: stream\n      };\n    } else if (segments) {\n      legacyOutput = {\n        case: \"segments\",\n        value: segments\n      };\n    }\n    let egressOptions;\n    if (opts) {\n      if (typeof opts === \"number\") {\n        egressOptions = {\n          case: \"preset\",\n          value: opts\n        };\n      } else {\n        egressOptions = {\n          case: \"advanced\",\n          value: opts\n        };\n      }\n    }\n    return {\n      output: legacyOutput,\n      options: egressOptions,\n      fileOutputs,\n      streamOutputs,\n      segmentOutputs,\n      imageOutputs\n    };\n  }\n  /**\n   * @param roomName - room name\n   * @param output - file or websocket output\n   * @param trackId - track Id\n   */\n  async startTrackEgress(roomName, output, trackId) {\n    let legacyOutput;\n    if (typeof output === \"string\") {\n      legacyOutput = {\n        case: \"websocketUrl\",\n        value: output\n      };\n    } else {\n      legacyOutput = {\n        case: \"file\",\n        value: output\n      };\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackEgressRequest({\n      roomName,\n      trackId,\n      output: legacyOutput\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"StartTrackEgress\",\n      req,\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param egressId -\n   * @param layout -\n   */\n  async updateLayout(egressId, layout) {\n    const data = await this.rpc.request(\n      svc,\n      \"UpdateLayout\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.UpdateLayoutRequest({ egressId, layout }).toJson(),\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param egressId -\n   * @param addOutputUrls -\n   * @param removeOutputUrls -\n   */\n  async updateStream(egressId, addOutputUrls, removeOutputUrls) {\n    addOutputUrls ??= [];\n    removeOutputUrls ??= [];\n    const data = await this.rpc.request(\n      svc,\n      \"UpdateStream\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.UpdateStreamRequest({ egressId, addOutputUrls, removeOutputUrls }).toJson(),\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param roomName - list egress for one room only\n   */\n  async listEgress(options) {\n    let req = {};\n    if (typeof options === \"string\") {\n      req.roomName = options;\n    } else if (options !== void 0) {\n      req = options;\n    }\n    const data = await this.rpc.request(\n      svc,\n      \"ListEgress\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListEgressRequest(req).toJson(),\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListEgressResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n  /**\n   * @param egressId -\n   */\n  async stopEgress(egressId) {\n    const data = await this.rpc.request(\n      svc,\n      \"StopEgress\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.StopEgressRequest({ egressId }).toJson(),\n      await this.authHeader({ roomRecord: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n}\n\n//# sourceMappingURL=EgressClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/EgressClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/IngressClient.js":
/*!***************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/IngressClient.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IngressClient: () => (/* binding */ IngressClient)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ServiceBase.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js\");\n/* harmony import */ var _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TwirpRPC.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js\");\n\n\n\nconst svc = \"Ingress\";\nclass IngressClient extends _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__.ServiceBase {\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host, apiKey, secret) {\n    super(apiKey, secret);\n    this.rpc = new _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.TwirpRpc(host, _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.livekitPackage);\n  }\n  /**\n   * @param inputType - protocol for the ingress\n   * @param opts - CreateIngressOptions\n   */\n  async createIngress(inputType, opts) {\n    let name = \"\";\n    let participantName = \"\";\n    let participantIdentity = \"\";\n    let bypassTranscoding = false;\n    let url = \"\";\n    if (opts == null) {\n      throw new Error(\"options dictionary is required\");\n    }\n    const roomName = opts.roomName;\n    const enableTranscoding = opts.enableTranscoding;\n    const audio = opts.audio;\n    const video = opts.video;\n    const participantMetadata = opts.participantMetadata;\n    name = opts.name || \"\";\n    participantName = opts.participantName || \"\";\n    participantIdentity = opts.participantIdentity || \"\";\n    bypassTranscoding = opts.bypassTranscoding || false;\n    url = opts.url || \"\";\n    if (typeof roomName == \"undefined\") {\n      throw new Error(\"required roomName option not provided\");\n    }\n    if (participantIdentity == \"\") {\n      throw new Error(\"required participantIdentity option not provided\");\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateIngressRequest({\n      inputType,\n      name,\n      roomName,\n      participantIdentity,\n      participantMetadata,\n      participantName,\n      bypassTranscoding,\n      enableTranscoding,\n      url,\n      audio,\n      video\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateIngress\",\n      req,\n      await this.authHeader({ ingressAdmin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param ingressId - ID of the ingress to update\n   * @param opts - UpdateIngressOptions\n   */\n  async updateIngress(ingressId, opts) {\n    const name = opts.name || \"\";\n    const roomName = opts.roomName || \"\";\n    const participantName = opts.participantName || \"\";\n    const participantIdentity = opts.participantIdentity || \"\";\n    const { participantMetadata } = opts;\n    const { audio, video, bypassTranscoding, enableTranscoding } = opts;\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.UpdateIngressRequest({\n      ingressId,\n      name,\n      roomName,\n      participantIdentity,\n      participantName,\n      participantMetadata,\n      bypassTranscoding,\n      enableTranscoding,\n      audio,\n      video\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"UpdateIngress\",\n      req,\n      await this.authHeader({ ingressAdmin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param arg - list room name or options\n   */\n  async listIngress(arg) {\n    let req = {};\n    if (typeof arg === \"string\") {\n      req.roomName = arg;\n    } else if (arg) {\n      req = arg;\n    }\n    const data = await this.rpc.request(\n      svc,\n      \"ListIngress\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListIngressRequest(req).toJson(),\n      await this.authHeader({ ingressAdmin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListIngressResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n  /**\n   * @param ingressId - ingress to delete\n   */\n  async deleteIngress(ingressId) {\n    const data = await this.rpc.request(\n      svc,\n      \"DeleteIngress\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DeleteIngressRequest({ ingressId }).toJson(),\n      await this.authHeader({ ingressAdmin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n}\n\n//# sourceMappingURL=IngressClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/IngressClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/RoomServiceClient.js":
/*!*******************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/RoomServiceClient.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoomServiceClient: () => (/* binding */ RoomServiceClient)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ServiceBase.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js\");\n/* harmony import */ var _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TwirpRPC.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js\");\n/* harmony import */ var _crypto_uuid_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./crypto/uuid.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/crypto/uuid.js\");\n\n\n\n\nconst svc = \"RoomService\";\nclass RoomServiceClient extends _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__.ServiceBase {\n  /**\n   *\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host, apiKey, secret) {\n    super(apiKey, secret);\n    this.rpc = new _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.TwirpRpc(host, _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.livekitPackage);\n  }\n  /**\n   * Creates a new room. Explicit room creation is not required, since rooms will\n   * be automatically created when the first participant joins. This method can be\n   * used to customize room settings.\n   * @param options -\n   */\n  async createRoom(options) {\n    const data = await this.rpc.request(\n      svc,\n      \"CreateRoom\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateRoomRequest(options).toJson(),\n      await this.authHeader({ roomCreate: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.Room.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * List active rooms\n   * @param names - when undefined or empty, list all rooms.\n   *                otherwise returns rooms with matching names\n   * @returns\n   */\n  async listRooms(names) {\n    const data = await this.rpc.request(\n      svc,\n      \"ListRooms\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListRoomsRequest({ names: names ?? [] }).toJson(),\n      await this.authHeader({ roomList: true })\n    );\n    const res = _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListRoomsResponse.fromJson(data, { ignoreUnknownFields: true });\n    return res.rooms ?? [];\n  }\n  async deleteRoom(room) {\n    await this.rpc.request(\n      svc,\n      \"DeleteRoom\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DeleteRoomRequest({ room }).toJson(),\n      await this.authHeader({ roomCreate: true })\n    );\n  }\n  /**\n   * Update metadata of a room\n   * @param room - name of the room\n   * @param metadata - the new metadata for the room\n   */\n  async updateRoomMetadata(room, metadata) {\n    const data = await this.rpc.request(\n      svc,\n      \"UpdateRoomMetadata\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.UpdateRoomMetadataRequest({ room, metadata }).toJson(),\n      await this.authHeader({ roomAdmin: true, room })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.Room.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * List participants in a room\n   * @param room - name of the room\n   */\n  async listParticipants(room) {\n    const data = await this.rpc.request(\n      svc,\n      \"ListParticipants\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListParticipantsRequest({ room }).toJson(),\n      await this.authHeader({ roomAdmin: true, room })\n    );\n    const res = _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListParticipantsResponse.fromJson(data, { ignoreUnknownFields: true });\n    return res.participants ?? [];\n  }\n  /**\n   * Get information on a specific participant, including the tracks that participant\n   * has published\n   * @param room - name of the room\n   * @param identity - identity of the participant to return\n   */\n  async getParticipant(room, identity) {\n    const data = await this.rpc.request(\n      svc,\n      \"GetParticipant\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.RoomParticipantIdentity({ room, identity }).toJson(),\n      await this.authHeader({ roomAdmin: true, room })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * Removes a participant in the room. This will disconnect the participant\n   * and will emit a Disconnected event for that participant.\n   * Even after being removed, the participant can still re-join the room.\n   * @param room -\n   * @param identity -\n   */\n  async removeParticipant(room, identity) {\n    await this.rpc.request(\n      svc,\n      \"RemoveParticipant\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.RoomParticipantIdentity({ room, identity }).toJson(),\n      await this.authHeader({ roomAdmin: true, room })\n    );\n  }\n  /**\n   * Forwards a participant's track to another room. This will create a\n   * participant to join the destination room that has same information\n   * with the source participant except the kind to be `Forwarded`. All\n   * changes to the source participant will be reflected to the forwarded\n   * participant. When the source participant disconnects or the\n   * `RemoveParticipant` method is called in the destination room, the\n   * forwarding will be stopped.\n   */\n  async forwardParticipant(room, identity, destinationRoom) {\n    await this.rpc.request(\n      svc,\n      \"ForwardParticipant\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ForwardParticipantRequest({ room, identity, destinationRoom }).toJson(),\n      await this.authHeader({ roomAdmin: true, room, destinationRoom })\n    );\n  }\n  /**\n   * Mutes a track that the participant has published.\n   * @param room -\n   * @param identity -\n   * @param trackSid - sid of the track to be muted\n   * @param muted - true to mute, false to unmute\n   */\n  async mutePublishedTrack(room, identity, trackSid, muted) {\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.MuteRoomTrackRequest({\n      room,\n      identity,\n      trackSid,\n      muted\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"MutePublishedTrack\",\n      req,\n      await this.authHeader({ roomAdmin: true, room })\n    );\n    const res = _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.MuteRoomTrackResponse.fromJson(data, { ignoreUnknownFields: true });\n    return res.track;\n  }\n  async updateParticipant(room, identity, metadataOrOptions, maybePermission, maybeName) {\n    const hasOptions = typeof metadataOrOptions === \"object\";\n    const metadata = hasOptions ? metadataOrOptions == null ? void 0 : metadataOrOptions.metadata : metadataOrOptions;\n    const permission = hasOptions ? metadataOrOptions.permission : maybePermission;\n    const name = hasOptions ? metadataOrOptions.name : maybeName;\n    const attributes = hasOptions ? metadataOrOptions.attributes : {};\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.UpdateParticipantRequest({\n      room,\n      identity,\n      attributes,\n      metadata,\n      name\n    });\n    if (permission) {\n      req.permission = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantPermission(permission);\n    }\n    const data = await this.rpc.request(\n      svc,\n      \"UpdateParticipant\",\n      req.toJson(),\n      await this.authHeader({ roomAdmin: true, room })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * Updates a participant's subscription to tracks\n   * @param room -\n   * @param identity -\n   * @param trackSids -\n   * @param subscribe - true to subscribe, false to unsubscribe\n   */\n  async updateSubscriptions(room, identity, trackSids, subscribe) {\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.UpdateSubscriptionsRequest({\n      room,\n      identity,\n      trackSids,\n      subscribe,\n      participantTracks: []\n    }).toJson();\n    await this.rpc.request(\n      svc,\n      \"UpdateSubscriptions\",\n      req,\n      await this.authHeader({ roomAdmin: true, room })\n    );\n  }\n  async sendData(room, data, kind, options = {}) {\n    const destinationSids = Array.isArray(options) ? options : options.destinationSids;\n    const topic = Array.isArray(options) ? void 0 : options.topic;\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SendDataRequest({\n      room,\n      data,\n      kind,\n      destinationSids: destinationSids ?? [],\n      topic\n    });\n    if (!Array.isArray(options) && options.destinationIdentities) {\n      req.destinationIdentities = options.destinationIdentities;\n    }\n    req.nonce = await (0,_crypto_uuid_js__WEBPACK_IMPORTED_MODULE_3__.getRandomBytes)(16);\n    await this.rpc.request(\n      svc,\n      \"SendData\",\n      req.toJson(),\n      await this.authHeader({ roomAdmin: true, room })\n    );\n  }\n}\n\n//# sourceMappingURL=RoomServiceClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/RoomServiceClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js":
/*!*************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/ServiceBase.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceBase: () => (/* binding */ ServiceBase)\n/* harmony export */ });\n/* harmony import */ var _AccessToken_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AccessToken.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/AccessToken.js\");\n\nclass ServiceBase {\n  /**\n   * @param apiKey - API Key.\n   * @param secret - API Secret.\n   * @param ttl - token TTL\n   */\n  constructor(apiKey, secret, ttl) {\n    this.apiKey = apiKey;\n    this.secret = secret;\n    this.ttl = ttl || \"10m\";\n  }\n  async authHeader(grant, sip) {\n    const at = new _AccessToken_js__WEBPACK_IMPORTED_MODULE_0__.AccessToken(this.apiKey, this.secret, { ttl: this.ttl });\n    if (grant) {\n      at.addGrant(grant);\n    }\n    if (sip) {\n      at.addSIPGrant(sip);\n    }\n    return {\n      Authorization: `Bearer ${await at.toJwt()}`\n    };\n  }\n}\n\n//# sourceMappingURL=ServiceBase.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGl2ZWtpdC1zZXJ2ZXItc2RrL2Rpc3QvU2VydmljZUJhc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHdEQUFXLDZCQUE2QixlQUFlO0FBQzFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLGlCQUFpQjtBQUNoRDtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdm9pY2UtYXNzaXN0YW50Mi8uL25vZGVfbW9kdWxlcy9saXZla2l0LXNlcnZlci1zZGsvZGlzdC9TZXJ2aWNlQmFzZS5qcz9jMTkzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFjY2Vzc1Rva2VuIH0gZnJvbSBcIi4vQWNjZXNzVG9rZW4uanNcIjtcbmNsYXNzIFNlcnZpY2VCYXNlIHtcbiAgLyoqXG4gICAqIEBwYXJhbSBhcGlLZXkgLSBBUEkgS2V5LlxuICAgKiBAcGFyYW0gc2VjcmV0IC0gQVBJIFNlY3JldC5cbiAgICogQHBhcmFtIHR0bCAtIHRva2VuIFRUTFxuICAgKi9cbiAgY29uc3RydWN0b3IoYXBpS2V5LCBzZWNyZXQsIHR0bCkge1xuICAgIHRoaXMuYXBpS2V5ID0gYXBpS2V5O1xuICAgIHRoaXMuc2VjcmV0ID0gc2VjcmV0O1xuICAgIHRoaXMudHRsID0gdHRsIHx8IFwiMTBtXCI7XG4gIH1cbiAgYXN5bmMgYXV0aEhlYWRlcihncmFudCwgc2lwKSB7XG4gICAgY29uc3QgYXQgPSBuZXcgQWNjZXNzVG9rZW4odGhpcy5hcGlLZXksIHRoaXMuc2VjcmV0LCB7IHR0bDogdGhpcy50dGwgfSk7XG4gICAgaWYgKGdyYW50KSB7XG4gICAgICBhdC5hZGRHcmFudChncmFudCk7XG4gICAgfVxuICAgIGlmIChzaXApIHtcbiAgICAgIGF0LmFkZFNJUEdyYW50KHNpcCk7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7YXdhaXQgYXQudG9Kd3QoKX1gXG4gICAgfTtcbiAgfVxufVxuZXhwb3J0IHtcbiAgU2VydmljZUJhc2Vcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TZXJ2aWNlQmFzZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/SipClient.js":
/*!***********************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/SipClient.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SipClient: () => (/* binding */ SipClient)\n/* harmony export */ });\n/* harmony import */ var _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @bufbuild/protobuf */ \"(rsc)/./node_modules/@bufbuild/protobuf/dist/esm/google/protobuf/duration_pb.js\");\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ServiceBase.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/ServiceBase.js\");\n/* harmony import */ var _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TwirpRPC.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js\");\n\n\n\n\nconst svc = \"SIP\";\nclass SipClient extends _ServiceBase_js__WEBPACK_IMPORTED_MODULE_1__.ServiceBase {\n  /**\n   * @param host - hostname including protocol. i.e. 'https://<project>.livekit.cloud'\n   * @param apiKey - API Key, can be set in env var LIVEKIT_API_KEY\n   * @param secret - API Secret, can be set in env var LIVEKIT_API_SECRET\n   */\n  constructor(host, apiKey, secret) {\n    super(apiKey, secret);\n    this.rpc = new _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.TwirpRpc(host, _TwirpRPC_js__WEBPACK_IMPORTED_MODULE_2__.livekitPackage);\n  }\n  /**\n   * @param number - phone number of the trunk\n   * @param opts - CreateSipTrunkOptions\n   * @deprecated use `createSipInboundTrunk` or `createSipOutboundTrunk`\n   */\n  async createSipTrunk(number, opts) {\n    let inboundAddresses;\n    let inboundNumbers;\n    let inboundUsername = \"\";\n    let inboundPassword = \"\";\n    let outboundAddress = \"\";\n    let outboundUsername = \"\";\n    let outboundPassword = \"\";\n    let name = \"\";\n    let metadata = \"\";\n    if (opts !== void 0) {\n      inboundAddresses = opts.inbound_addresses;\n      inboundNumbers = opts.inbound_numbers;\n      inboundUsername = opts.inbound_username || \"\";\n      inboundPassword = opts.inbound_password || \"\";\n      outboundAddress = opts.outbound_address || \"\";\n      outboundUsername = opts.outbound_username || \"\";\n      outboundPassword = opts.outbound_password || \"\";\n      name = opts.name || \"\";\n      metadata = opts.metadata || \"\";\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateSIPTrunkRequest({\n      name,\n      metadata,\n      inboundAddresses,\n      inboundNumbers,\n      inboundUsername,\n      inboundPassword,\n      outboundNumber: number,\n      outboundAddress,\n      outboundUsername,\n      outboundPassword\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateSIPTrunk\",\n      req,\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param name - human-readable name of the trunk\n   * @param numbers - phone numbers of the trunk\n   * @param opts - CreateSipTrunkOptions\n   */\n  async createSipInboundTrunk(name, numbers, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateSIPInboundTrunkRequest({\n      trunk: new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPInboundTrunkInfo({\n        name,\n        numbers,\n        metadata: opts == null ? void 0 : opts.metadata,\n        allowedAddresses: opts.allowedAddresses ?? opts.allowed_addresses,\n        allowedNumbers: opts.allowedNumbers ?? opts.allowed_numbers,\n        authUsername: opts.authUsername ?? opts.auth_username,\n        authPassword: opts.authPassword ?? opts.auth_password,\n        headers: opts.headers,\n        headersToAttributes: opts.headersToAttributes,\n        includeHeaders: opts.includeHeaders,\n        krispEnabled: opts.krispEnabled\n      })\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateSIPInboundTrunk\",\n      req,\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPInboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param name - human-readable name of the trunk\n   * @param address - hostname and port of the SIP server to dial\n   * @param numbers - phone numbers of the trunk\n   * @param opts - CreateSipTrunkOptions\n   */\n  async createSipOutboundTrunk(name, address, numbers, opts) {\n    if (opts === void 0) {\n      opts = {\n        transport: _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPTransport.SIP_TRANSPORT_AUTO\n      };\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateSIPOutboundTrunkRequest({\n      trunk: new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPOutboundTrunkInfo({\n        name,\n        address,\n        numbers,\n        metadata: opts.metadata,\n        transport: opts.transport,\n        authUsername: opts.authUsername ?? opts.auth_username,\n        authPassword: opts.authPassword ?? opts.auth_password,\n        headers: opts.headers,\n        headersToAttributes: opts.headersToAttributes,\n        includeHeaders: opts.includeHeaders\n      })\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateSIPOutboundTrunk\",\n      req,\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPOutboundTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @deprecated use `listSipInboundTrunk` or `listSipOutboundTrunk`\n   */\n  async listSipTrunk() {\n    const req = {};\n    const data = await this.rpc.request(\n      svc,\n      \"ListSIPTrunk\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPTrunkRequest(req).toJson(),\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPTrunkResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n  async listSipInboundTrunk() {\n    const req = {};\n    const data = await this.rpc.request(\n      svc,\n      \"ListSIPInboundTrunk\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPInboundTrunkRequest(req).toJson(),\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPInboundTrunkResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n  async listSipOutboundTrunk() {\n    const req = {};\n    const data = await this.rpc.request(\n      svc,\n      \"ListSIPOutboundTrunk\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPOutboundTrunkRequest(req).toJson(),\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPOutboundTrunkResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n  /**\n   * @param sipTrunkId - sip trunk to delete\n   */\n  async deleteSipTrunk(sipTrunkId) {\n    const data = await this.rpc.request(\n      svc,\n      \"DeleteSIPTrunk\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DeleteSIPTrunkRequest({ sipTrunkId }).toJson(),\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPTrunkInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param rule - sip dispatch rule\n   * @param opts - CreateSipDispatchRuleOptions\n   */\n  async createSipDispatchRule(rule, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    let ruleProto = void 0;\n    if (rule.type == \"direct\") {\n      ruleProto = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRule({\n        rule: {\n          case: \"dispatchRuleDirect\",\n          value: new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRuleDirect({\n            roomName: rule.roomName,\n            pin: rule.pin || \"\"\n          })\n        }\n      });\n    } else if (rule.type == \"individual\") {\n      ruleProto = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRule({\n        rule: {\n          case: \"dispatchRuleIndividual\",\n          value: new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRuleIndividual({\n            roomPrefix: rule.roomPrefix,\n            pin: rule.pin || \"\"\n          })\n        }\n      });\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateSIPDispatchRuleRequest({\n      rule: ruleProto,\n      trunkIds: opts.trunkIds,\n      hidePhoneNumber: opts.hidePhoneNumber,\n      name: opts.name,\n      metadata: opts.metadata,\n      attributes: opts.attributes,\n      roomPreset: opts.roomPreset,\n      roomConfig: opts.roomConfig\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateSIPDispatchRule\",\n      req,\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRuleInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  async listSipDispatchRule() {\n    const req = {};\n    const data = await this.rpc.request(\n      svc,\n      \"ListSIPDispatchRule\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPDispatchRuleRequest(req).toJson(),\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ListSIPDispatchRuleResponse.fromJson(data, { ignoreUnknownFields: true }).items ?? [];\n  }\n  /**\n   * @param sipDispatchRuleId - sip trunk to delete\n   */\n  async deleteSipDispatchRule(sipDispatchRuleId) {\n    const data = await this.rpc.request(\n      svc,\n      \"DeleteSIPDispatchRule\",\n      new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DeleteSIPDispatchRuleRequest({ sipDispatchRuleId }).toJson(),\n      await this.authHeader({}, { admin: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRuleInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param sipTrunkId - sip trunk to use for the call\n   * @param number - number to dial\n   * @param roomName - room to attach the call to\n   * @param opts - CreateSipParticipantOptions\n   */\n  async createSipParticipant(sipTrunkId, number, roomName, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.CreateSIPParticipantRequest({\n      sipTrunkId,\n      sipCallTo: number,\n      sipNumber: opts.fromNumber,\n      roomName,\n      participantIdentity: opts.participantIdentity || \"sip-participant\",\n      participantName: opts.participantName,\n      participantMetadata: opts.participantMetadata,\n      participantAttributes: opts.participantAttributes,\n      dtmf: opts.dtmf,\n      playDialtone: opts.playDialtone ?? opts.playRingtone,\n      headers: opts.headers,\n      hidePhoneNumber: opts.hidePhoneNumber,\n      includeHeaders: opts.includeHeaders,\n      ringingTimeout: opts.ringingTimeout ? new _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.Duration({ seconds: BigInt(opts.ringingTimeout) }) : void 0,\n      maxCallDuration: opts.maxCallDuration ? new _bufbuild_protobuf__WEBPACK_IMPORTED_MODULE_3__.Duration({ seconds: BigInt(opts.maxCallDuration) }) : void 0,\n      krispEnabled: opts.krispEnabled\n    }).toJson();\n    const data = await this.rpc.request(\n      svc,\n      \"CreateSIPParticipant\",\n      req,\n      await this.authHeader({}, { call: true })\n    );\n    return _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPParticipantInfo.fromJson(data, { ignoreUnknownFields: true });\n  }\n  /**\n   * @param roomName - room the SIP participant to transfer is connectd to\n   * @param participantIdentity - identity of the SIP participant to transfer\n   * @param transferTo - SIP URL to transfer the participant to\n   */\n  async transferSipParticipant(roomName, participantIdentity, transferTo, opts) {\n    if (opts === void 0) {\n      opts = {};\n    }\n    const req = new _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TransferSIPParticipantRequest({\n      participantIdentity,\n      roomName,\n      transferTo,\n      playDialtone: opts.playDialtone,\n      headers: opts.headers\n    }).toJson();\n    await this.rpc.request(\n      svc,\n      \"TransferSIPParticipant\",\n      req,\n      await this.authHeader({ roomAdmin: true, room: roomName }, { call: true })\n    );\n  }\n}\n\n//# sourceMappingURL=SipClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/SipClient.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js":
/*!**********************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/TwirpRPC.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TwirpError: () => (/* binding */ TwirpError),\n/* harmony export */   TwirpRpc: () => (/* binding */ TwirpRpc),\n/* harmony export */   livekitPackage: () => (/* binding */ livekitPackage)\n/* harmony export */ });\nconst defaultPrefix = \"/twirp\";\nconst livekitPackage = \"livekit\";\nclass TwirpError extends Error {\n  constructor(name, message, status, code) {\n    super(message);\n    this.name = name;\n    this.status = status;\n    this.code = code;\n  }\n}\nclass TwirpRpc {\n  constructor(host, pkg, prefix) {\n    if (host.startsWith(\"ws\")) {\n      host = host.replace(\"ws\", \"http\");\n    }\n    this.host = host;\n    this.pkg = pkg;\n    this.prefix = prefix || defaultPrefix;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  async request(service, method, data, headers) {\n    const path = `${this.prefix}/${this.pkg}.${service}/${method}`;\n    const url = new URL(path, this.host);\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json;charset=UTF-8\",\n        ...headers\n      },\n      body: JSON.stringify(data)\n    });\n    if (!response.ok) {\n      const isJson = response.headers.get(\"content-type\") === \"application/json\";\n      let errorMessage = \"Unknown internal error\";\n      let errorCode = void 0;\n      try {\n        if (isJson) {\n          const parsedError = await response.json();\n          if (\"msg\" in parsedError) {\n            errorMessage = parsedError.msg;\n          }\n          if (\"code\" in parsedError) {\n            errorCode = parsedError.code;\n          }\n        } else {\n          errorMessage = await response.text();\n        }\n      } catch (e) {\n        console.debug(`Error when trying to parse error message, using defaults`, e);\n      }\n      throw new TwirpError(response.statusText, errorMessage, response.status, errorCode);\n    }\n    const parsedResp = await response.json();\n    const camelcaseKeys = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/quick-lru\"), __webpack_require__.e(\"vendor-chunks/map-obj\"), __webpack_require__.e(\"vendor-chunks/camelcase\"), __webpack_require__.e(\"vendor-chunks/camelcase-keys\")]).then(__webpack_require__.bind(__webpack_require__, /*! camelcase-keys */ \"(rsc)/./node_modules/camelcase-keys/index.js\")).then((mod) => mod.default);\n    return camelcaseKeys(parsedResp, { deep: true });\n  }\n}\n\n//# sourceMappingURL=TwirpRPC.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/TwirpRPC.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/WebhookReceiver.js":
/*!*****************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/WebhookReceiver.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebhookEvent: () => (/* binding */ WebhookEvent),\n/* harmony export */   WebhookReceiver: () => (/* binding */ WebhookReceiver),\n/* harmony export */   authorizeHeader: () => (/* binding */ authorizeHeader)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _AccessToken_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AccessToken.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/AccessToken.js\");\n/* harmony import */ var _crypto_digest_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./crypto/digest.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/crypto/digest.js\");\n\n\n\nconst authorizeHeader = \"Authorize\";\nclass WebhookEvent extends _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.WebhookEvent {\n  constructor() {\n    super(...arguments);\n    this.event = \"\";\n  }\n  static fromBinary(bytes, options) {\n    return new WebhookEvent().fromBinary(bytes, options);\n  }\n  static fromJson(jsonValue, options) {\n    return new WebhookEvent().fromJson(jsonValue, options);\n  }\n  static fromJsonString(jsonString, options) {\n    return new WebhookEvent().fromJsonString(jsonString, options);\n  }\n}\nclass WebhookReceiver {\n  constructor(apiKey, apiSecret) {\n    this.verifier = new _AccessToken_js__WEBPACK_IMPORTED_MODULE_1__.TokenVerifier(apiKey, apiSecret);\n  }\n  /**\n   * @param body - string of the posted body\n   * @param authHeader - `Authorization` header from the request\n   * @param skipAuth - true to skip auth validation\n   * @param clockTolerance - How much tolerance to allow for checks against the auth header to be skewed from the claims\n   * @returns The processed webhook event\n   */\n  async receive(body, authHeader, skipAuth = false, clockTolerance) {\n    if (!skipAuth) {\n      if (!authHeader) {\n        throw new Error(\"authorization header is empty\");\n      }\n      const claims = await this.verifier.verify(authHeader, clockTolerance);\n      const hash = await (0,_crypto_digest_js__WEBPACK_IMPORTED_MODULE_2__.digest)(body);\n      const hashDecoded = btoa(\n        Array.from(new Uint8Array(hash)).map((v) => String.fromCharCode(v)).join(\"\")\n      );\n      if (claims.sha256 !== hashDecoded) {\n        throw new Error(\"sha256 checksum of body does not match\");\n      }\n    }\n    return WebhookEvent.fromJson(JSON.parse(body), { ignoreUnknownFields: true });\n  }\n}\n\n//# sourceMappingURL=WebhookReceiver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/WebhookReceiver.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/crypto/digest.js":
/*!***************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/crypto/digest.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   digest: () => (/* binding */ digest)\n/* harmony export */ });\nasync function digest(data) {\n  var _a;\n  if ((_a = globalThis.crypto) == null ? void 0 : _a.subtle) {\n    const encoder = new TextEncoder();\n    return crypto.subtle.digest(\"SHA-256\", encoder.encode(data));\n  } else {\n    const nodeCrypto = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! node:crypto */ \"node:crypto\", 19));\n    return nodeCrypto.createHash(\"sha256\").update(data).digest();\n  }\n}\n\n//# sourceMappingURL=digest.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGl2ZWtpdC1zZXJ2ZXItc2RrL2Rpc3QvY3J5cHRvL2RpZ2VzdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiw2QkFBNkIsNEhBQXFCO0FBQ2xEO0FBQ0E7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vbm9kZV9tb2R1bGVzL2xpdmVraXQtc2VydmVyLXNkay9kaXN0L2NyeXB0by9kaWdlc3QuanM/OWIwNyJdLCJzb3VyY2VzQ29udGVudCI6WyJhc3luYyBmdW5jdGlvbiBkaWdlc3QoZGF0YSkge1xuICB2YXIgX2E7XG4gIGlmICgoX2EgPSBnbG9iYWxUaGlzLmNyeXB0bykgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnN1YnRsZSkge1xuICAgIGNvbnN0IGVuY29kZXIgPSBuZXcgVGV4dEVuY29kZXIoKTtcbiAgICByZXR1cm4gY3J5cHRvLnN1YnRsZS5kaWdlc3QoXCJTSEEtMjU2XCIsIGVuY29kZXIuZW5jb2RlKGRhdGEpKTtcbiAgfSBlbHNlIHtcbiAgICBjb25zdCBub2RlQ3J5cHRvID0gYXdhaXQgaW1wb3J0KFwibm9kZTpjcnlwdG9cIik7XG4gICAgcmV0dXJuIG5vZGVDcnlwdG8uY3JlYXRlSGFzaChcInNoYTI1NlwiKS51cGRhdGUoZGF0YSkuZGlnZXN0KCk7XG4gIH1cbn1cbmV4cG9ydCB7XG4gIGRpZ2VzdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWRpZ2VzdC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/crypto/digest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/crypto/uuid.js":
/*!*************************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/crypto/uuid.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomBytes: () => (/* binding */ getRandomBytes)\n/* harmony export */ });\nasync function getRandomBytes(size = 16) {\n  if (globalThis.crypto) {\n    return crypto.getRandomValues(new Uint8Array(size));\n  } else {\n    const nodeCrypto = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! node:crypto */ \"node:crypto\", 19));\n    return nodeCrypto.getRandomValues(new Uint8Array(size));\n  }\n}\n\n//# sourceMappingURL=uuid.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGl2ZWtpdC1zZXJ2ZXItc2RrL2Rpc3QvY3J5cHRvL3V1aWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSiw2QkFBNkIsNEhBQXFCO0FBQ2xEO0FBQ0E7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vbm9kZV9tb2R1bGVzL2xpdmVraXQtc2VydmVyLXNkay9kaXN0L2NyeXB0by91dWlkLmpzPzI0YWIiXSwic291cmNlc0NvbnRlbnQiOlsiYXN5bmMgZnVuY3Rpb24gZ2V0UmFuZG9tQnl0ZXMoc2l6ZSA9IDE2KSB7XG4gIGlmIChnbG9iYWxUaGlzLmNyeXB0bykge1xuICAgIHJldHVybiBjcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50OEFycmF5KHNpemUpKTtcbiAgfSBlbHNlIHtcbiAgICBjb25zdCBub2RlQ3J5cHRvID0gYXdhaXQgaW1wb3J0KFwibm9kZTpjcnlwdG9cIik7XG4gICAgcmV0dXJuIG5vZGVDcnlwdG8uZ2V0UmFuZG9tVmFsdWVzKG5ldyBVaW50OEFycmF5KHNpemUpKTtcbiAgfVxufVxuZXhwb3J0IHtcbiAgZ2V0UmFuZG9tQnl0ZXNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11dWlkLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/crypto/uuid.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/grants.js":
/*!********************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/grants.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   claimsToJwtPayload: () => (/* binding */ claimsToJwtPayload),\n/* harmony export */   trackSourceToString: () => (/* binding */ trackSourceToString)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n\nfunction trackSourceToString(source) {\n  switch (source) {\n    case _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackSource.CAMERA:\n      return \"camera\";\n    case _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackSource.MICROPHONE:\n      return \"microphone\";\n    case _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackSource.SCREEN_SHARE:\n      return \"screen_share\";\n    case _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackSource.SCREEN_SHARE_AUDIO:\n      return \"screen_share_audio\";\n    default:\n      throw new TypeError(`Cannot convert TrackSource ${source} to string`);\n  }\n}\nfunction claimsToJwtPayload(grant) {\n  var _a;\n  const claim = { ...grant };\n  if (Array.isArray((_a = claim.video) == null ? void 0 : _a.canPublishSources)) {\n    claim.video.canPublishSources = claim.video.canPublishSources.map(trackSourceToString);\n  }\n  return claim;\n}\n\n//# sourceMappingURL=grants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGl2ZWtpdC1zZXJ2ZXItc2RrL2Rpc3QvZ3JhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUNoRDtBQUNBO0FBQ0EsU0FBUywwREFBVztBQUNwQjtBQUNBLFNBQVMsMERBQVc7QUFDcEI7QUFDQSxTQUFTLDBEQUFXO0FBQ3BCO0FBQ0EsU0FBUywwREFBVztBQUNwQjtBQUNBO0FBQ0Esd0RBQXdELFFBQVE7QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly92b2ljZS1hc3Npc3RhbnQyLy4vbm9kZV9tb2R1bGVzL2xpdmVraXQtc2VydmVyLXNkay9kaXN0L2dyYW50cy5qcz80ZjUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFRyYWNrU291cmNlIH0gZnJvbSBcIkBsaXZla2l0L3Byb3RvY29sXCI7XG5mdW5jdGlvbiB0cmFja1NvdXJjZVRvU3RyaW5nKHNvdXJjZSkge1xuICBzd2l0Y2ggKHNvdXJjZSkge1xuICAgIGNhc2UgVHJhY2tTb3VyY2UuQ0FNRVJBOlxuICAgICAgcmV0dXJuIFwiY2FtZXJhXCI7XG4gICAgY2FzZSBUcmFja1NvdXJjZS5NSUNST1BIT05FOlxuICAgICAgcmV0dXJuIFwibWljcm9waG9uZVwiO1xuICAgIGNhc2UgVHJhY2tTb3VyY2UuU0NSRUVOX1NIQVJFOlxuICAgICAgcmV0dXJuIFwic2NyZWVuX3NoYXJlXCI7XG4gICAgY2FzZSBUcmFja1NvdXJjZS5TQ1JFRU5fU0hBUkVfQVVESU86XG4gICAgICByZXR1cm4gXCJzY3JlZW5fc2hhcmVfYXVkaW9cIjtcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgQ2Fubm90IGNvbnZlcnQgVHJhY2tTb3VyY2UgJHtzb3VyY2V9IHRvIHN0cmluZ2ApO1xuICB9XG59XG5mdW5jdGlvbiBjbGFpbXNUb0p3dFBheWxvYWQoZ3JhbnQpIHtcbiAgdmFyIF9hO1xuICBjb25zdCBjbGFpbSA9IHsgLi4uZ3JhbnQgfTtcbiAgaWYgKEFycmF5LmlzQXJyYXkoKF9hID0gY2xhaW0udmlkZW8pID09IG51bGwgPyB2b2lkIDAgOiBfYS5jYW5QdWJsaXNoU291cmNlcykpIHtcbiAgICBjbGFpbS52aWRlby5jYW5QdWJsaXNoU291cmNlcyA9IGNsYWltLnZpZGVvLmNhblB1Ymxpc2hTb3VyY2VzLm1hcCh0cmFja1NvdXJjZVRvU3RyaW5nKTtcbiAgfVxuICByZXR1cm4gY2xhaW07XG59XG5leHBvcnQge1xuICBjbGFpbXNUb0p3dFBheWxvYWQsXG4gIHRyYWNrU291cmNlVG9TdHJpbmdcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ncmFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/grants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/livekit-server-sdk/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/livekit-server-sdk/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessToken: () => (/* reexport safe */ _AccessToken_js__WEBPACK_IMPORTED_MODULE_1__.AccessToken),\n/* harmony export */   AgentDispatchClient: () => (/* reexport safe */ _AgentDispatchClient_js__WEBPACK_IMPORTED_MODULE_2__.AgentDispatchClient),\n/* harmony export */   AliOSSUpload: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.AliOSSUpload),\n/* harmony export */   AudioCodec: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.AudioCodec),\n/* harmony export */   AutoParticipantEgress: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.AutoParticipantEgress),\n/* harmony export */   AutoTrackEgress: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.AutoTrackEgress),\n/* harmony export */   AzureBlobUpload: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.AzureBlobUpload),\n/* harmony export */   DataPacket_Kind: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DataPacket_Kind),\n/* harmony export */   DirectFileOutput: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.DirectFileOutput),\n/* harmony export */   EgressClient: () => (/* reexport safe */ _EgressClient_js__WEBPACK_IMPORTED_MODULE_3__.EgressClient),\n/* harmony export */   EgressInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressInfo),\n/* harmony export */   EgressStatus: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EgressStatus),\n/* harmony export */   EncodedFileOutput: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EncodedFileOutput),\n/* harmony export */   EncodedFileType: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EncodedFileType),\n/* harmony export */   EncodingOptions: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EncodingOptions),\n/* harmony export */   EncodingOptionsPreset: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.EncodingOptionsPreset),\n/* harmony export */   GCPUpload: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.GCPUpload),\n/* harmony export */   ImageCodec: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ImageCodec),\n/* harmony export */   ImageFileSuffix: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ImageFileSuffix),\n/* harmony export */   ImageOutput: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ImageOutput),\n/* harmony export */   IngressAudioEncodingOptions: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressAudioEncodingOptions),\n/* harmony export */   IngressAudioEncodingPreset: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressAudioEncodingPreset),\n/* harmony export */   IngressAudioOptions: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressAudioOptions),\n/* harmony export */   IngressClient: () => (/* reexport safe */ _IngressClient_js__WEBPACK_IMPORTED_MODULE_5__.IngressClient),\n/* harmony export */   IngressInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressInfo),\n/* harmony export */   IngressInput: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressInput),\n/* harmony export */   IngressState: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressState),\n/* harmony export */   IngressVideoEncodingOptions: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressVideoEncodingOptions),\n/* harmony export */   IngressVideoEncodingPreset: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressVideoEncodingPreset),\n/* harmony export */   IngressVideoOptions: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.IngressVideoOptions),\n/* harmony export */   ParticipantEgressRequest: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantEgressRequest),\n/* harmony export */   ParticipantInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantInfo),\n/* harmony export */   ParticipantInfo_State: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantInfo_State),\n/* harmony export */   ParticipantPermission: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.ParticipantPermission),\n/* harmony export */   Room: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.Room),\n/* harmony export */   RoomCompositeEgressRequest: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.RoomCompositeEgressRequest),\n/* harmony export */   RoomEgress: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.RoomEgress),\n/* harmony export */   RoomServiceClient: () => (/* reexport safe */ _RoomServiceClient_js__WEBPACK_IMPORTED_MODULE_6__.RoomServiceClient),\n/* harmony export */   S3Upload: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.S3Upload),\n/* harmony export */   SIPDispatchRuleInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPDispatchRuleInfo),\n/* harmony export */   SIPParticipantInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPParticipantInfo),\n/* harmony export */   SIPTrunkInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SIPTrunkInfo),\n/* harmony export */   SegmentedFileOutput: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SegmentedFileOutput),\n/* harmony export */   SegmentedFileProtocol: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.SegmentedFileProtocol),\n/* harmony export */   SipClient: () => (/* reexport safe */ _SipClient_js__WEBPACK_IMPORTED_MODULE_7__.SipClient),\n/* harmony export */   StreamOutput: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.StreamOutput),\n/* harmony export */   StreamProtocol: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.StreamProtocol),\n/* harmony export */   TokenVerifier: () => (/* reexport safe */ _AccessToken_js__WEBPACK_IMPORTED_MODULE_1__.TokenVerifier),\n/* harmony export */   TrackCompositeEgressRequest: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackCompositeEgressRequest),\n/* harmony export */   TrackEgressRequest: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackEgressRequest),\n/* harmony export */   TrackInfo: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackInfo),\n/* harmony export */   TrackSource: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackSource),\n/* harmony export */   TrackType: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.TrackType),\n/* harmony export */   VideoCodec: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.VideoCodec),\n/* harmony export */   WebEgressRequest: () => (/* reexport safe */ _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__.WebEgressRequest),\n/* harmony export */   WebhookEvent: () => (/* reexport safe */ _WebhookReceiver_js__WEBPACK_IMPORTED_MODULE_8__.WebhookEvent),\n/* harmony export */   WebhookReceiver: () => (/* reexport safe */ _WebhookReceiver_js__WEBPACK_IMPORTED_MODULE_8__.WebhookReceiver),\n/* harmony export */   authorizeHeader: () => (/* reexport safe */ _WebhookReceiver_js__WEBPACK_IMPORTED_MODULE_8__.authorizeHeader),\n/* harmony export */   claimsToJwtPayload: () => (/* reexport safe */ _grants_js__WEBPACK_IMPORTED_MODULE_4__.claimsToJwtPayload),\n/* harmony export */   trackSourceToString: () => (/* reexport safe */ _grants_js__WEBPACK_IMPORTED_MODULE_4__.trackSourceToString)\n/* harmony export */ });\n/* harmony import */ var _livekit_protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @livekit/protocol */ \"(rsc)/./node_modules/@livekit/protocol/dist/index.mjs\");\n/* harmony import */ var _AccessToken_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AccessToken.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/AccessToken.js\");\n/* harmony import */ var _AgentDispatchClient_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AgentDispatchClient.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/AgentDispatchClient.js\");\n/* harmony import */ var _EgressClient_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EgressClient.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/EgressClient.js\");\n/* harmony import */ var _grants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./grants.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/grants.js\");\n/* harmony import */ var _IngressClient_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./IngressClient.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/IngressClient.js\");\n/* harmony import */ var _RoomServiceClient_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./RoomServiceClient.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/RoomServiceClient.js\");\n/* harmony import */ var _SipClient_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./SipClient.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/SipClient.js\");\n/* harmony import */ var _WebhookReceiver_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./WebhookReceiver.js */ \"(rsc)/./node_modules/livekit-server-sdk/dist/WebhookReceiver.js\");\n\n\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/livekit-server-sdk/dist/index.js\n");

/***/ })

};
;