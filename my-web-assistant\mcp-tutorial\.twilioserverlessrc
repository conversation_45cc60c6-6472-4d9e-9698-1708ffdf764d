{
	"commands": {},
	"environments": {},
	"projects": {},
	// "assets": true 	/* Upload assets. Can be turned off with --no-assets */,
	// "assetsFolder": null 	/* Specific folder name to be used for static assets */,
	// "buildSid": null 	/* An existing Build SID to deploy to the new environment */,
	// "createEnvironment": false 	/* Creates environment if it couldn't find it. */,
	// "cwd": null 	/* Sets the directory of your existing Serverless project. Defaults to current directory */,
	// "detailedLogs": false 	/* Toggles detailed request logging by showing request body and query params */,
	// "edge": null 	/* Twilio API Region */,
	// "env": null 	/* Path to .env file for environment variables that should be installed */,
	// "environment": "dev" 	/* The environment name (domain suffix) you want to use for your deployment. Alternatively you can specify an environment SID starting with ZE. */,
	// "extendedOutput": false 	/* Show an extended set of properties on the output */,
	// "force": false 	/* Will run deployment in force mode. Can be dangerous. */,
	// "forkProcess": true 	/* Disable forking function processes to emulate production environment */,
	// "functionSid": null 	/* Specific Function SID to retrieve logs for */,
	// "functions": true 	/* Upload functions. Can be turned off with --no-functions */,
	// "functionsFolder": null 	/* Specific folder name to be used for static functions */,
	// "inspect": null 	/* Enables Node.js debugging protocol */,
	// "inspectBrk": null 	/* Enables Node.js debugging protocol, stops execution until debugger is attached */,
	// "legacyMode": false 	/* Enables legacy mode, it will prefix your asset paths with /assets */,
	// "live": true 	/* Always serve from the current functions (no caching) */,
	// "loadLocalEnv": false 	/* Includes the local environment variables */,
	// "loadSystemEnv": false 	/* Uses system environment variables as fallback for variables specified in your .env file. Needs to be used with --env explicitly specified. */,
	// "logCacheSize": null 	/* Tailing the log endpoint will cache previously seen entries to avoid duplicates. The cache is topped at a maximum of 1000 by default. This option can change that. */,
	// "logLevel": "info" 	/* Level of logging messages. */,
	// "logs": true 	/* Toggles request logging */,
	// "ngrok": null 	/* Uses ngrok to create a public url. Pass a string to set the subdomain (requires a paid-for ngrok account). */,
	// "outputFormat": "" 	/* Output the results in a different format */,
	// "overrideExistingProject": false 	/* Deploys Serverless project to existing service if a naming conflict has been found. */,
	// "port": "3000" 	/* Override default port of 3000 */,
	// "production": false 	/* Promote build to the production environment (no domain suffix). Overrides environment flag */,
	// "properties": null 	/* Specify the output properties you want to see. Works best on single types */,
	// "region": null 	/* Twilio API Region */,
	"runtime": "node22" 	/* The version of Node.js to deploy the build to. (node22) */,
	// "serviceName": null 	/* Overrides the name of the Serverless project. Default: the name field in your package.json */,
	// "serviceSid": null 	/* SID of the Twilio Serverless Service to deploy to */,
	// "sourceEnvironment": null 	/* SID or suffix of an existing environment you want to deploy from. */,
	// "tail": false 	/* Continuously stream the logs */,
	// "template": null 	/* undefined */,
}