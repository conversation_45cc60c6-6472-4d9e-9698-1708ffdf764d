"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/camelcase-keys";
exports.ids = ["vendor-chunks/camelcase-keys"];
exports.modules = {

/***/ "(rsc)/./node_modules/camelcase-keys/index.js":
/*!**********************************************!*\
  !*** ./node_modules/camelcase-keys/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ camelcaseKeys)\n/* harmony export */ });\n/* harmony import */ var map_obj__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! map-obj */ \"(rsc)/./node_modules/map-obj/index.js\");\n/* harmony import */ var camelcase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! camelcase */ \"(rsc)/./node_modules/camelcase/index.js\");\n/* harmony import */ var quick_lru__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! quick-lru */ \"(rsc)/./node_modules/quick-lru/index.js\");\n\n\n\n\nconst has = (array, key) => array.some(element => {\n\tif (typeof element === 'string') {\n\t\treturn element === key;\n\t}\n\n\telement.lastIndex = 0;\n\n\treturn element.test(key);\n});\n\nconst cache = new quick_lru__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({maxSize: 100_000});\n\n// Reproduces behavior from `map-obj`.\nconst isObject = value =>\n\ttypeof value === 'object'\n\t\t&& value !== null\n\t\t&& !(value instanceof RegExp)\n\t\t&& !(value instanceof Error)\n\t\t&& !(value instanceof Date);\n\nconst transform = (input, options = {}) => {\n\tif (!isObject(input)) {\n\t\treturn input;\n\t}\n\n\tconst {\n\t\texclude,\n\t\tpascalCase = false,\n\t\tstopPaths,\n\t\tdeep = false,\n\t\tpreserveConsecutiveUppercase = false,\n\t} = options;\n\n\tconst stopPathsSet = new Set(stopPaths);\n\n\tconst makeMapper = parentPath => (key, value) => {\n\t\tif (deep && isObject(value)) {\n\t\t\tconst path = parentPath === undefined ? key : `${parentPath}.${key}`;\n\n\t\t\tif (!stopPathsSet.has(path)) {\n\t\t\t\tvalue = (0,map_obj__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value, makeMapper(path));\n\t\t\t}\n\t\t}\n\n\t\tif (!(exclude && has(exclude, key))) {\n\t\t\tconst cacheKey = pascalCase ? `${key}_` : key;\n\n\t\t\tif (cache.has(cacheKey)) {\n\t\t\t\tkey = cache.get(cacheKey);\n\t\t\t} else {\n\t\t\t\tconst returnValue = (0,camelcase__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, {pascalCase, locale: false, preserveConsecutiveUppercase});\n\n\t\t\t\tif (key.length < 100) { // Prevent abuse\n\t\t\t\t\tcache.set(cacheKey, returnValue);\n\t\t\t\t}\n\n\t\t\t\tkey = returnValue;\n\t\t\t}\n\t\t}\n\n\t\treturn [key, value];\n\t};\n\n\treturn (0,map_obj__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(input, makeMapper(undefined));\n};\n\nfunction camelcaseKeys(input, options) {\n\tif (Array.isArray(input)) {\n\t\treturn Object.keys(input).map(key => transform(input[key], options));\n\t}\n\n\treturn transform(input, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/camelcase-keys/index.js\n");

/***/ })

};
;