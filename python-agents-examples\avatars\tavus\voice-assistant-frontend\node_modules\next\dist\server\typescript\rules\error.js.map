{"version": 3, "sources": ["../../../../src/server/typescript/rules/error.ts"], "names": ["errorEntry", "getSemanticDiagnostics", "source", "isClientEntry", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "ts", "getTs", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "INVALID_ERROR_COMPONENT", "messageText", "start", "length", "text"], "mappings": "AAAA,8FAA8F;;;;;+BAmC9F;;;eAAA;;;0BAjC+B;uBACT;AAGtB,MAAMA,aAAa;IACjBC,wBACEC,MAA2B,EAC3BC,aAAsB;QAEtB,MAAMC,cAAc,oBAAoBC,IAAI,CAACH,OAAOI,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAACH,OAAOI,QAAQ;QAEzE,IAAI,CAACF,eAAe,CAACG,mBAAmB,OAAO,EAAE;QAEjD,MAAMC,KAAKC,IAAAA,YAAK;QAEhB,IAAI,CAACN,eAAe;YAClB,6CAA6C;YAC7C,OAAO;gBACL;oBACEO,MAAMR;oBACNS,UAAUH,GAAGI,kBAAkB,CAACC,KAAK;oBACrCC,MAAMC,wBAAc,CAACC,uBAAuB;oBAC5CC,aAAa,CAAC,mJAAmJ,CAAC;oBAClKC,OAAO;oBACPC,QAAQjB,OAAOkB,IAAI,CAACD,MAAM;gBAC5B;aACD;QACH;QACA,OAAO,EAAE;IACX;AACF;MAEA,WAAenB"}