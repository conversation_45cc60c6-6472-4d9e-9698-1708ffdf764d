{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "names": ["ImageError", "ImageOptimizerCache", "detectContentType", "fetchExternalImage", "fetchInternalImage", "getHash", "getImageSize", "getMaxAge", "imageOptimizer", "optimizeImage", "sendResponse", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "CACHE_VERSION", "ANIMATABLE_TYPES", "VECTOR_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "sharp", "require", "process", "env", "NEXT_SHARP_PATH", "concurrency", "divisor", "NODE_ENV", "Math", "floor", "max", "cpus", "length", "e", "showSharpMissingWarning", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "replace", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "etag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "every", "b", "i", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "Log", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "parseUrl", "test", "decodeURIComponent", "pathname", "hasLocalMatch", "hrefParsed", "URL", "toString", "_error", "protocol", "hasRemoteMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "revalidateAfter", "curRevalidate", "isStale", "_", "set", "revalidate", "Error", "err", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "contentType", "height", "nextConfigOutput", "optimizedBuffer", "transformer", "sequentialRead", "rotate", "resize", "undefined", "withoutEnlargement", "avif", "avifQuality", "chromaSubsampling", "webp", "png", "jpeg", "progressive", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "getOrientation", "operations", "Orientation", "RIGHT_TOP", "type", "numRotations", "BOTTOM_RIGHT", "LEFT_BOTTOM", "processBuffer", "res", "fetch", "ok", "status", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "cacheControl", "_req", "_res", "handleRequest", "mocked", "createRequestResponseMocks", "method", "socket", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageUpstream", "paramsResult", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "isAnimated", "getExtension", "output", "getMetadata", "meta", "opts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "byteLength", "end", "metadata", "decodeBuffer", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAoZaA,UAAU;eAAVA;;IAvPAC,mBAAmB;eAAnBA;;IArCGC,iBAAiB;eAAjBA;;IA4bMC,kBAAkB;eAAlBA;;IAkBAC,kBAAkB;eAAlBA;;IA/eNC,OAAO;eAAPA;;IA2uBMC,YAAY;eAAZA;;IA7YNC,SAAS;eAATA;;IA2LMC,cAAc;eAAdA;;IA5KAC,aAAa;eAAbA;;IA8VNC,YAAY;eAAZA;;;wBAlyBW;oBACF;oBACJ;wBAEK;2EACK;gCACa;kEACpB;mEACD;sBACF;4DAC4B;8BAEjB;mCAEF;oCACC;6BAEY;6BAWV;6BACY;6DACxB;sBACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIzB,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACP;IAAMC;IAAKE;CAAI;AACzC,MAAMK,eAAe;IAACJ;CAAI;AAC1B,MAAMK,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEJ,IAAI;IACFA,QAAQC,QAAQC,QAAQC,GAAG,CAACC,eAAe,IAAI;IAC/C,IAAIJ,SAASA,MAAMK,WAAW,KAAK,GAAG;QACpC,2DAA2D;QAC3D,8DAA8D;QAC9D,0DAA0D;QAC1D,MAAMC,UAAUJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,gBAAgB,IAAI;QAC7DP,MAAMK,WAAW,CAACG,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACC,IAAAA,QAAI,IAAGC,MAAM,GAAGN,SAAS;IACjE;AACF,EAAE,OAAOO,GAAG;AACV,iEAAiE;AACnE;AAEA,IAAIC,0BAA0BZ,QAAQC,GAAG,CAACI,QAAQ,KAAK;AAmBvD,SAASQ,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAASpC,QAAQuC,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,qDAAqD;IACrD,OAAOF,KAAKK,MAAM,CAAC,UAAUC,OAAO,CAAC,OAAO;AAC9C;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdC,IAAY;IAEZ,MAAMC,WAAWC,IAAAA,UAAI,EAACP,KAAK,CAAC,EAAEE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEE,KAAK,CAAC,EAAEJ,UAAU,CAAC;IAEvE,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,SAASvD,kBAAkBuD,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACW,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACvD,OAAOvD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACsD,KAAK,CACpD,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAE1B;QACA,OAAOxD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACuD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOtD;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAACqD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOzD;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAACwD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAOrD;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAACoD,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAO1D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOpD;IACT;IACA,OAAO;AACT;AAEO,MAAMhB;IAIX,OAAOsE,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQ7C,MAAM,GAAG,GAAG;YACtBuD,KAAIC,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACL,KAAK;YACR,OAAO;gBAAEM,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACR,MAAM;YAC7B,OAAO;gBAAEM,cAAc;YAAqC;QAC9D;QAEA,IAAIN,IAAInD,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEyD,cAAc;YAA8B;QACvD;QAEA,IAAIN,IAAIS,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIV,IAAIS,UAAU,CAAC,MAAM;gBAKAE;YAJvBR,OAAOH;YACPU,aAAa;YACb,IACE,uBAAuBE,IAAI,CACzBC,mBAAmBF,EAAAA,YAAAA,IAAAA,cAAQ,EAACX,yBAATW,UAAeG,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLR,cAAc;gBAChB;YACF;YACA,IAAI,CAACS,IAAAA,gCAAa,EAACjB,eAAeE,MAAM;gBACtC,OAAO;oBAAEM,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIU;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIjB;gBACrBG,OAAOa,WAAWE,QAAQ;gBAC1BR,aAAa;YACf,EAAE,OAAOS,QAAQ;gBACf,OAAO;oBAAEb,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAACjD,QAAQ,CAAC2D,WAAWI,QAAQ,GAAG;gBACtD,OAAO;oBAAEd,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACe,IAAAA,kCAAc,EAAC3B,SAASG,gBAAgBmB,aAAa;gBACxD,OAAO;oBAAEV,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA2C;QACpE;QAEA,IAAI,CAACJ,GAAG;YACN,OAAO;gBAAEI,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACN,IAAI;YAC3B,OAAO;gBAAEI,cAAc;YAA6C;QACtE;QAEA,MAAMgB,QAAQC,SAAStB,GAAG;QAE1B,IAAIqB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLhB,cAAc;YAChB;QACF;QAEA,MAAMmB,QAAQ;eAAKjC,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACToC,MAAMC,IAAI,CAAC3F;QACb;QAEA,MAAM4F,cACJF,MAAMpE,QAAQ,CAACiE,UAAWjC,SAASiC,SAASvF;QAE9C,IAAI,CAAC4F,aAAa;YAChB,OAAO;gBACLrB,cAAc,CAAC,yBAAyB,EAAEgB,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASrB;QAEzB,IAAIsB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLtB,cACE;YACJ;QACF;QAEA,IAAIP,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAU2B,IAAI,CAAC1F;YACjB;YAEA,IAAI,CAAC+D,UAAU1C,QAAQ,CAACuE,UAAU;gBAChC,OAAO;oBACLtB,cAAc,CAAC,2BAA2B,EAAEJ,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAM/C,WAAWH,qBAAqB4C,WAAW,EAAE,EAAEV,IAAI2C,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW9B,IAAIS,UAAU,CAC7B,CAAC,EAAErB,WAAW2C,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACL5B;YACAsB;YACAf;YACAoB;YACAR;YACAM;YACAzE;YACAwC;QACF;IACF;IAEA,OAAOqC,YAAY,EACjB7B,IAAI,EACJmB,KAAK,EACLM,OAAO,EACPzE,QAAQ,EAMT,EAAU;QACT,OAAOpC,QAAQ;YAACa;YAAeuE;YAAMmB;YAAOM;YAASzE;SAAS;IAChE;IAEA8E,YAAY,EACVC,OAAO,EACP9C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC+C,QAAQ,GAAG7D,IAAAA,UAAI,EAAC4D,SAAS,SAAS;QACvC,IAAI,CAAC9C,UAAU,GAAGA;IACpB;IAEA,MAAMgD,IAAIC,QAAgB,EAAyC;QACjE,IAAI;YACF,MAAMF,WAAW7D,IAAAA,UAAI,EAAC,IAAI,CAAC6D,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAM/D,YAAQ,CAACgE,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYxE,MAAMJ,UAAU,GAAG0E,KAAKG,KAAK,CAAC,KAAK;gBAChE,MAAM1E,SAAS,MAAMI,YAAQ,CAACuE,QAAQ,CAACxE,IAAAA,UAAI,EAAC6D,UAAUO;gBACtD,MAAMxE,WAAW6E,OAAOH;gBACxB,MAAM3E,SAAS8E,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAM;wBACN7E;wBACAD;wBACAH;oBACF;oBACAkF,iBACEzG,KAAKE,GAAG,CAACsB,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D8C,KAAKD,GAAG;oBACVW,eAAelF;oBACfmF,SAASZ,MAAMtE;gBACjB;YACF;QACF,EAAE,OAAOmF,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJjB,QAAgB,EAChBW,KAAmC,EACnC,EACEO,UAAU,EAGX,EACD;QACA,IAAIP,CAAAA,yBAAAA,MAAOC,IAAI,MAAK,SAAS;YAC3B,MAAM,IAAIO,MAAM;QAClB;QAEA,IAAI,OAAOD,eAAe,UAAU;YAClC,MAAM,IAAIC,MAAM;QAClB;QACA,MAAMtF,WACJzB,KAAKE,GAAG,CAAC4G,YAAY,IAAI,CAACnE,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D8C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAM1E,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAAC6D,QAAQ,EAAEE,WACpBW,MAAMhF,SAAS,EACfuF,YACArF,UACA8E,MAAM7E,MAAM,EACZ6E,MAAM5E,IAAI;QAEd,EAAE,OAAOqF,KAAK;YACZrD,KAAIsD,KAAK,CAAC,CAAC,+BAA+B,EAAErB,SAAS,CAAC,EAAEoB;QAC1D;IACF;AACF;AACO,MAAM/I,mBAAmB8I;IAG9BvB,YAAY0B,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIjB,KAAK,CAAC,KAAM;QACpC,IAAI,CAACqB,KAAKlB,MAAM,GAAGiB,UAAUE,IAAI,GAAGtB,KAAK,CAAC,KAAK;QAC/CqB,MAAMA,IAAIE,WAAW;QACrB,IAAIpB,OAAO;YACTA,QAAQA,MAAMoB,WAAW;QAC3B;QACAL,IAAIT,GAAG,CAACY,KAAKlB;IACf;IACA,OAAOe;AACT;AAEO,SAAS9I,UAAU6I,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAI3B,GAAG,CAAC,eAAe2B,IAAI3B,GAAG,CAAC,cAAc;QACvD,IAAIiC,IAAI5D,UAAU,CAAC,QAAQ4D,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIjD,SAAS8C,KAAK;QACxB,IAAI,CAAC7C,MAAMgD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AAEO,eAAerJ,cAAc,EAClCgD,MAAM,EACNsG,WAAW,EACX7C,OAAO,EACPN,KAAK,EACLoD,MAAM,EACNC,gBAAgB,EAQjB;IACC,IAAIC,kBAAkBzG;IACtB,IAAIlC,OAAO;QACT,mCAAmC;QACnC,MAAM4I,cAAc5I,MAAMkC,QAAQ;YAChC2G,gBAAgB;QAClB;QAEAD,YAAYE,MAAM;QAElB,IAAIL,QAAQ;YACVG,YAAYG,MAAM,CAAC1D,OAAOoD;QAC5B,OAAO;YACLG,YAAYG,MAAM,CAAC1D,OAAO2D,WAAW;gBACnCC,oBAAoB;YACtB;QACF;QAEA,IAAIT,gBAAgBpJ,MAAM;YACxB,IAAIwJ,YAAYM,IAAI,EAAE;gBACpB,MAAMC,cAAcxD,UAAU;gBAC9BiD,YAAYM,IAAI,CAAC;oBACfvD,SAASnF,KAAKE,GAAG,CAACyI,aAAa;oBAC/BC,mBAAmB;gBACrB;YACF,OAAO;gBACLjF,KAAIC,QAAQ,CACV,CAAC,wIAAwI,CAAC,GACxI;gBAEJwE,YAAYS,IAAI,CAAC;oBAAE1D;gBAAQ;YAC7B;QACF,OAAO,IAAI6C,gBAAgBnJ,MAAM;YAC/BuJ,YAAYS,IAAI,CAAC;gBAAE1D;YAAQ;QAC7B,OAAO,IAAI6C,gBAAgBlJ,KAAK;YAC9BsJ,YAAYU,GAAG,CAAC;gBAAE3D;YAAQ;QAC5B,OAAO,IAAI6C,gBAAgBjJ,MAAM;YAC/BqJ,YAAYW,IAAI,CAAC;gBAAE5D;gBAAS6D,aAAa;YAAK;QAChD;QAEAb,kBAAkB,MAAMC,YAAYa,QAAQ;IAC5C,iCAAiC;IACnC,OAAO;QACL,IAAI3I,2BAA2B4H,qBAAqB,cAAc;YAChEvE,KAAIsD,KAAK,CACP,CAAC,0LAA0L,CAAC;YAE9L,MAAM,IAAIhJ,WAAW,KAAK;QAC5B;QACA,wCAAwC;QACxC,IAAIqC,yBAAyB;YAC3BqD,KAAIC,QAAQ,CACV,CAAC,wLAAwL,CAAC,GACxL;YAEJtD,0BAA0B;QAC5B;QAEA,qCAAqC;QACrC,MAAM4I,cAAc,MAAMC,IAAAA,8BAAc,EAACzH;QAEzC,MAAM0H,aAA0B,EAAE;QAElC,IAAIF,gBAAgBG,2BAAW,CAACC,SAAS,EAAE;YACzCF,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACI,YAAY,EAAE;YACnDL,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO,IAAIN,gBAAgBG,2BAAW,CAACK,WAAW,EAAE;YAClDN,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAUC,cAAc;YAAE;QACpD,OAAO;QACL,kCAAkC;QAClC,6DAA6D;QAC7D,+BAA+B;QACjC;QAEA,IAAIvB,QAAQ;YACVmB,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;gBAAOoD;YAAO;QAClD,OAAO;YACLmB,WAAWnE,IAAI,CAAC;gBAAEsE,MAAM;gBAAU1E;YAAM;QAC1C;QAEA,MAAM,EAAE8E,aAAa,EAAE,GACrBlK,QAAQ;QAEV,IAAIuI,gBAAgBpJ,MAAM;YACxBuJ,kBAAkB,MAAMwB,cAAcjI,QAAQ0H,YAAY,QAAQjE;QACpE,OAAO,IAAI6C,gBAAgBnJ,MAAM;YAC/BsJ,kBAAkB,MAAMwB,cAAcjI,QAAQ0H,YAAY,QAAQjE;QACpE,OAAO,IAAI6C,gBAAgBlJ,KAAK;YAC9BqJ,kBAAkB,MAAMwB,cAAcjI,QAAQ0H,YAAY,OAAOjE;QACnE,OAAO,IAAI6C,gBAAgBjJ,MAAM;YAC/BoJ,kBAAkB,MAAMwB,cAAcjI,QAAQ0H,YAAY,QAAQjE;QACpE;IACF;IAEA,OAAOgD;AACT;AAEO,eAAe/J,mBAAmBsF,IAAY;IACnD,MAAMkG,MAAM,MAAMC,MAAMnG;IAExB,IAAI,CAACkG,IAAIE,EAAE,EAAE;QACXnG,KAAIsD,KAAK,CAAC,sCAAsCvD,MAAMkG,IAAIG,MAAM;QAChE,MAAM,IAAI9L,WACR2L,IAAIG,MAAM,EACV;IAEJ;IAEA,MAAMrI,SAASsI,OAAOC,IAAI,CAAC,MAAML,IAAIM,WAAW;IAChD,MAAMlC,cAAc4B,IAAIxE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMwE,eAAeP,IAAIxE,OAAO,CAACO,GAAG,CAAC;IAErC,OAAO;QAAEjE;QAAQsG;QAAamC;IAAa;AAC7C;AAEO,eAAe9L,mBACpBqF,IAAY,EACZ0G,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxCjH,KAAKG;YACL+G,QAAQL,KAAKK,MAAM,IAAI;YACvBrF,SAASgF,KAAKhF,OAAO;YACrBsF,QAAQN,KAAKM,MAAM;QACrB;QAEA,MAAMJ,cAAcC,OAAO9H,GAAG,EAAE8H,OAAOX,GAAG,EAAEe,YAAO,CAACC,KAAK,CAAClH,MAAM;QAChE,MAAM6G,OAAOX,GAAG,CAACiB,WAAW;QAE5B,IAAI,CAACN,OAAOX,GAAG,CAAC1C,UAAU,EAAE;YAC1BvD,KAAIsD,KAAK,CAAC,6BAA6BvD,MAAM6G,OAAOX,GAAG,CAAC1C,UAAU;YAClE,MAAM,IAAIjJ,WACRsM,OAAOX,GAAG,CAAC1C,UAAU,EACrB;QAEJ;QAEA,MAAMxF,SAASsI,OAAOc,MAAM,CAACP,OAAOX,GAAG,CAACmB,OAAO;QAC/C,MAAM/C,cAAcuC,OAAOX,GAAG,CAACoB,SAAS,CAAC;QACzC,MAAMb,eAAeI,OAAOX,GAAG,CAACoB,SAAS,CAAC;QAC1C,OAAO;YAAEtJ;YAAQsG;YAAamC;QAAa;IAC7C,EAAE,OAAOnD,KAAK;QACZrD,KAAIsD,KAAK,CAAC,sCAAsCvD,MAAMsD;QACtD,MAAM,IAAI/I,WACR,KACA;IAEJ;AACF;AAEO,eAAeQ,eACpBwM,aAA4B,EAC5BC,YAGC,EACDvI,UAMC,EACDC,KAA0B;QAOxBqI;IALF,MAAM,EAAEvH,IAAI,EAAEyB,OAAO,EAAEN,KAAK,EAAEnE,QAAQ,EAAE,GAAGwK;IAC3C,MAAMC,iBAAiBF,cAAcvJ,MAAM;IAC3C,MAAMF,SAAShD,UAAUyM,cAAcd,YAAY;IACnD,MAAMiB,eACJjN,kBAAkBgN,qBAClBF,6BAAAA,cAAcjD,WAAW,qBAAzBiD,2BAA2BtD,WAAW,GAAGD,IAAI;IAE/C,IAAI0D,cAAc;QAChB,IACEA,aAAapH,UAAU,CAAC,gBACxB,CAACrB,WAAWG,MAAM,CAACuI,mBAAmB,EACtC;YACA1H,KAAIsD,KAAK,CACP,CAAC,wBAAwB,EAAEvD,KAAK,YAAY,EAAE0H,aAAa,qCAAqC,CAAC;YAEnG,MAAM,IAAInN,WACR,KACA;QAEJ;QAEA,IAAImB,iBAAiBwB,QAAQ,CAACwK,iBAAiBE,IAAAA,mBAAU,EAACH,iBAAiB;YACzExH,KAAIC,QAAQ,CACV,CAAC,wBAAwB,EAAEF,KAAK,8GAA8G,CAAC;YAEjJ,OAAO;gBAAEhC,QAAQyJ;gBAAgBnD,aAAaoD;gBAAc5J;YAAO;QACrE;QACA,IAAInC,aAAauB,QAAQ,CAACwK,eAAe;YACvC,wEAAwE;YACxE,6DAA6D;YAC7D,4EAA4E;YAC5E,OAAO;gBAAE1J,QAAQyJ;gBAAgBnD,aAAaoD;gBAAc5J;YAAO;QACrE;QACA,IAAI,CAAC4J,aAAapH,UAAU,CAAC,aAAaoH,aAAaxK,QAAQ,CAAC,MAAM;YACpE+C,KAAIsD,KAAK,CACP,kDACAvD,MACA,YACA0H;YAEF,MAAM,IAAInN,WAAW,KAAK;QAC5B;IACF;IAEA,IAAI+J;IAEJ,IAAItH,UAAU;QACZsH,cAActH;IAChB,OAAO,IACL0K,CAAAA,gCAAAA,aAAcpH,UAAU,CAAC,cACzBuH,IAAAA,yBAAY,EAACH,iBACbA,iBAAiBvM,QACjBuM,iBAAiBxM,MACjB;QACAoJ,cAAcoD;IAChB,OAAO;QACLpD,cAAcjJ;IAChB;IACA,IAAI;QACF,IAAIoJ,kBAAkB,MAAMzJ,cAAc;YACxCgD,QAAQyJ;YACRnD;YACA7C;YACAN;YACAqD,kBAAkBvF,WAAW6I,MAAM;QACrC;QACA,IAAIrD,iBAAiB;YACnB,IAAIvF,SAASiC,SAASvF,iBAAiB6F,YAAY5F,cAAc;gBAC/D,MAAM,EAAEkM,WAAW,EAAE,GACnBhM,QAAQ;gBACV,8EAA8E;gBAC9E,gFAAgF;gBAChF,qFAAqF;gBACrF,MAAMiM,OAAO,MAAMD,YAAYtD;gBAC/B,MAAMwD,OAAO;oBACXC,WAAWF,KAAK7G,KAAK;oBACrBgH,YAAYH,KAAKzD,MAAM;oBACvB6D,aAAa,CAAC,KAAK,EAAE9D,YAAY,QAAQ,EAAEG,gBAAgB1D,QAAQ,CACjE,UACA,CAAC;gBACL;gBACA0D,kBAAkB6B,OAAOC,IAAI,CAAC8B,SAASC,IAAAA,6BAAe,EAACL;gBACvD3D,cAAc;YAChB;YACA,OAAO;gBACLtG,QAAQyG;gBACRH;gBACAxG,QAAQxB,KAAKE,GAAG,CAACsB,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC5D;QACF,OAAO;YACL,MAAM,IAAIjF,WAAW,KAAK;QAC5B;IACF,EAAE,OAAOgJ,OAAO;QACd,IAAIkE,kBAAkBC,cAAc;YAClC,yDAAyD;YACzD,OAAO;gBACL1J,QAAQyJ;gBACRnD,aAAaoD;gBACb5J,QAAQmB,WAAWG,MAAM,CAACI,eAAe;YAC3C;QACF,OAAO;YACL,MAAM,IAAIjF,WACR,KACA;QAEJ;IACF;AACF;AAEA,SAASgO,yBACP1I,GAAW,EACXyE,WAA0B;IAE1B,MAAM,CAACkE,sBAAsB,GAAG3I,IAAI6C,KAAK,CAAC,KAAK;IAC/C,MAAM+F,wBAAwBD,sBAAsB9F,KAAK,CAAC,KAAKgG,GAAG;IAClE,IAAI,CAACpE,eAAe,CAACmE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsB/F,KAAK,CAAC,KAAK;IACpD,MAAM7E,YAAYgK,IAAAA,yBAAY,EAACvD;IAC/B,OAAO,CAAC,EAAEqE,SAAS,CAAC,EAAE9K,UAAU,CAAC;AACnC;AAEA,SAAS+K,mBACP7J,GAAoB,EACpBmH,GAAmB,EACnBrG,GAAW,EACX5B,IAAY,EACZqG,WAA0B,EAC1B3C,QAAiB,EACjBkH,MAAoB,EACpBC,YAAiC,EACjChL,MAAc,EACdoB,KAAc;IAEdgH,IAAI6C,SAAS,CAAC,QAAQ;IACtB7C,IAAI6C,SAAS,CACX,iBACApH,WACI,yCACA,CAAC,gBAAgB,EAAEzC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAIkL,IAAAA,6BAAgB,EAACjK,KAAKmH,KAAKjI,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEgL,UAAU;QAAK;IAC1B;IACA,IAAI3E,aAAa;QACf4B,IAAI6C,SAAS,CAAC,gBAAgBzE;IAChC;IAEA,MAAMqE,WAAWJ,yBAAyB1I,KAAKyE;IAC/C4B,IAAI6C,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAE9C,MAAMiD,aAAaK,sBAAsB;IAAC;IAG3EjD,IAAI6C,SAAS,CAAC,2BAA2BD,aAAaM,qBAAqB;IAC3ElD,IAAI6C,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAAShO,aACd8D,GAAoB,EACpBmH,GAAmB,EACnBrG,GAAW,EACXhC,SAAiB,EACjBG,MAAc,EACd2D,QAAiB,EACjBkH,MAAoB,EACpBC,YAAiC,EACjChL,MAAc,EACdoB,KAAc;IAEd,MAAMoF,cAAc+E,IAAAA,2BAAc,EAACxL;IACnC,MAAMI,OAAOrD,QAAQ;QAACoD;KAAO;IAC7B,MAAMsL,SAASV,mBACb7J,KACAmH,KACArG,KACA5B,MACAqG,aACA3C,UACAkH,QACAC,cACAhL,QACAoB;IAEF,IAAI,CAACoK,OAAOL,QAAQ,EAAE;QACpB/C,IAAI6C,SAAS,CAAC,kBAAkBzC,OAAOiD,UAAU,CAACvL;QAClDkI,IAAIsD,GAAG,CAACxL;IACV;AACF;AAEO,eAAenD,aACpBmD,MAAc,EACd,8BAA8B;AAC9BH,SAA2C;IAK3C,qDAAqD;IACrD,0DAA0D;IAC1D,IAAIA,cAAc,QAAQ;QACxB,IAAI/B,OAAO;YACT,MAAM4I,cAAc5I,MAAMkC;YAC1B,MAAM,EAAEmD,KAAK,EAAEoD,MAAM,EAAE,GAAG,MAAMG,YAAY+E,QAAQ;YACpD,OAAO;gBAAEtI;gBAAOoD;YAAO;QACzB,OAAO;YACL,MAAM,EAAEmF,YAAY,EAAE,GACpB3N,QAAQ;YACV,MAAM,EAAEoF,KAAK,EAAEoD,MAAM,EAAE,GAAG,MAAMmF,aAAa1L;YAC7C,OAAO;gBAAEmD;gBAAOoD;YAAO;QACzB;IACF;IAEA,MAAM,EAAEpD,KAAK,EAAEoD,MAAM,EAAE,GAAGoF,IAAAA,kBAAW,EAAC3L;IACtC,OAAO;QAAEmD;QAAOoD;IAAO;AACzB"}