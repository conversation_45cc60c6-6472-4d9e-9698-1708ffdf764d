{"version": 3, "sources": ["../../../src/server/web/edge-route-module-wrapper.ts"], "names": ["adapter", "IncrementalCache", "RouteMatcher", "internal_getCurrentFunctionWaitUntil", "getUtils", "searchParamsToUrlQuery", "getEdgePreviewProps", "EdgeRouteModuleWrapper", "routeModule", "matcher", "definition", "wrap", "options", "wrapper", "opts", "handler", "bind", "request", "evt", "utils", "pageIsDynamic", "isDynamic", "page", "pathname", "basePath", "nextUrl", "rewrites", "caseSensitive", "params", "normalizeDynamicRouteParams", "searchParams", "previewProps", "context", "prerenderManifest", "version", "routes", "dynamicRoutes", "preview", "notFoundRoutes", "renderOpts", "supportsDynamicResponse", "experimental", "ppr", "res", "handle", "waitUntilPromises", "waitUntil", "push", "Promise", "all"], "mappings": "AAMA,OAAO,YAAW;AAElB,SAASA,OAAO,QAA6B,YAAW;AACxD,SAASC,gBAAgB,QAAQ,2BAA0B;AAC3D,SAASC,YAAY,QAAQ,yCAAwC;AAErE,SAASC,oCAAoC,QAAQ,6BAA4B;AACjF,SAASC,QAAQ,QAAQ,kBAAiB;AAC1C,SAASC,sBAAsB,QAAQ,4CAA2C;AAClF,SAASC,mBAAmB,QAAQ,2BAA0B;AAI9D;;;;CAIC,GACD,OAAO,MAAMC;IAGX;;;;;GAKC,GACD,YAAoB,AAAiBC,WAAgC,CAAE;aAAlCA,cAAAA;QACnC,wEAAwE;QACxE,IAAI,CAACC,OAAO,GAAG,IAAIP,aAAaM,YAAYE,UAAU;IACxD;IAEA;;;;;;;;GAQC,GACD,OAAcC,KACZH,WAAgC,EAChCI,UAAuB,CAAC,CAAC,EACzB;QACA,6BAA6B;QAC7B,MAAMC,UAAU,IAAIN,uBAAuBC;QAE3C,gCAAgC;QAChC,OAAO,CAACM;YACN,OAAOd,QAAQ;gBACb,GAAGc,IAAI;gBACP,GAAGF,OAAO;gBACVX;gBACA,kEAAkE;gBAClEc,SAASF,QAAQE,OAAO,CAACC,IAAI,CAACH;YAChC;QACF;IACF;IAEA,MAAcE,QACZE,OAAoB,EACpBC,GAAmB,EACA;QACnB,MAAMC,QAAQf,SAAS;YACrBgB,eAAe,IAAI,CAACX,OAAO,CAACY,SAAS;YACrCC,MAAM,IAAI,CAACb,OAAO,CAACC,UAAU,CAACa,QAAQ;YACtCC,UAAUP,QAAQQ,OAAO,CAACD,QAAQ;YAClC,2EAA2E;YAC3EE,UAAU,CAAC;YACX,qEAAqE;YACrEC,eAAe;QACjB;QAEA,MAAM,EAAEC,MAAM,EAAE,GAAGT,MAAMU,2BAA2B,CAClDxB,uBAAuBY,QAAQQ,OAAO,CAACK,YAAY;QAGrD,MAAMC,eAAezB;QAErB,wEAAwE;QACxE,kBAAkB;QAClB,MAAM0B,UAAuC;YAC3CJ;YACAK,mBAAmB;gBACjBC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAASN;gBACTO,gBAAgB,EAAE;YACpB;YACAC,YAAY;gBACVC,yBAAyB;gBACzB,mCAAmC;gBACnCC,cAAc;oBAAEC,KAAK;gBAAM;YAC7B;QACF;QAEA,qCAAqC;QACrC,MAAMC,MAAM,MAAM,IAAI,CAACnC,WAAW,CAACoC,MAAM,CAAC3B,SAASe;QAEnD,MAAMa,oBAAoB;YAAC1C;SAAuC;QAClE,IAAI6B,QAAQO,UAAU,CAACO,SAAS,EAAE;YAChCD,kBAAkBE,IAAI,CAACf,QAAQO,UAAU,CAACO,SAAS;QACrD;QACA5B,IAAI4B,SAAS,CAACE,QAAQC,GAAG,CAACJ;QAE1B,OAAOF;IACT;AACF"}