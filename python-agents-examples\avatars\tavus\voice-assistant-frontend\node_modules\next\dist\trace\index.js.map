{"version": 3, "sources": ["../../src/trace/index.ts"], "names": ["Span", "SpanStatus", "exportTraceState", "flushAllTraces", "getTraceEvents", "initializeTraceState", "recordTraceEvents", "setGlobal", "trace"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAoBEA,IAAI;eAAJA,WAAI;;IAEJC,UAAU;eAAVA,iBAAU;;IAPVC,gBAAgB;eAAhBA,uBAAgB;;IAChBC,cAAc;eAAdA,qBAAc;;IACdC,cAAc;eAAdA,qBAAc;;IACdC,oBAAoB;eAApBA,2BAAoB;;IACpBC,iBAAiB;eAAjBA,wBAAiB;;IAEjBC,SAAS;eAATA,iBAAS;;IAPTC,KAAK;eAALA,YAAK;;;uBALA;wBACmB"}