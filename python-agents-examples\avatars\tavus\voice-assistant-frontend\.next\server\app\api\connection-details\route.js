"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/connection-details/route";
exports.ids = ["app/api/connection-details/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnection-details%2Froute&page=%2Fapi%2Fconnection-details%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnection-details%2Froute.ts&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnection-details%2Froute&page=%2Fapi%2Fconnection-details%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnection-details%2Froute.ts&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Work_Projects_2025_livekitdemo_python_agents_examples_avatars_tavus_voice_assistant_frontend_app_api_connection_details_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/connection-details/route.ts */ \"(rsc)/./app/api/connection-details/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/connection-details/route\",\n        pathname: \"/api/connection-details\",\n        filename: \"route\",\n        bundlePath: \"app/api/connection-details/route\"\n    },\n    resolvedPagePath: \"D:\\\\Work\\\\Projects\\\\2025\\\\livekitdemo\\\\python-agents-examples\\\\avatars\\\\tavus\\\\voice-assistant-frontend\\\\app\\\\api\\\\connection-details\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Work_Projects_2025_livekitdemo_python_agents_examples_avatars_tavus_voice_assistant_frontend_app_api_connection_details_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/connection-details/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnection-details%2Froute&page=%2Fapi%2Fconnection-details%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnection-details%2Froute.ts&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/connection-details/route.ts":
/*!*********************************************!*\
  !*** ./app/api/connection-details/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var livekit_server_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! livekit-server-sdk */ \"(rsc)/./node_modules/livekit-server-sdk/dist/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n\n// NOTE: you are expected to define the following environment variables in `.env.local`:\nconst API_KEY = process.env.LIVEKIT_API_KEY;\nconst API_SECRET = process.env.LIVEKIT_API_SECRET;\nconst LIVEKIT_URL = process.env.LIVEKIT_URL;\n// don't cache the results\nconst revalidate = 0;\nasync function GET() {\n    try {\n        if (LIVEKIT_URL === undefined) {\n            throw new Error(\"LIVEKIT_URL is not defined\");\n        }\n        if (API_KEY === undefined) {\n            throw new Error(\"LIVEKIT_API_KEY is not defined\");\n        }\n        if (API_SECRET === undefined) {\n            throw new Error(\"LIVEKIT_API_SECRET is not defined\");\n        }\n        // Generate participant token\n        const participantIdentity = `voice_assistant_user_${Math.floor(Math.random() * 10000)}`;\n        const roomName = `voice_assistant_room_${Math.floor(Math.random() * 10000)}`;\n        const participantToken = await createParticipantToken({\n            identity: participantIdentity\n        }, roomName);\n        // Return connection details\n        const data = {\n            serverUrl: LIVEKIT_URL,\n            roomName,\n            participantToken: participantToken,\n            participantName: participantIdentity\n        };\n        const headers = new Headers({\n            \"Cache-Control\": \"no-store\"\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json(data, {\n            headers\n        });\n    } catch (error) {\n        if (error instanceof Error) {\n            console.error(error);\n            return new next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse(error.message, {\n                status: 500\n            });\n        }\n    }\n}\nfunction createParticipantToken(userInfo, roomName) {\n    const at = new livekit_server_sdk__WEBPACK_IMPORTED_MODULE_0__.AccessToken(API_KEY, API_SECRET, {\n        ...userInfo,\n        ttl: \"15m\"\n    });\n    const grant = {\n        room: roomName,\n        roomJoin: true,\n        canPublish: true,\n        canPublishData: true,\n        canSubscribe: true\n    };\n    at.addGrant(grant);\n    return at.toJwt();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/connection-details/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@livekit","vendor-chunks/jose","vendor-chunks/@bufbuild","vendor-chunks/livekit-server-sdk"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconnection-details%2Froute&page=%2Fapi%2Fconnection-details%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconnection-details%2Froute.ts&appDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CWork%5CProjects%5C2025%5Clivekitdemo%5Cpython-agents-examples%5Cavatars%5Ctavus%5Cvoice-assistant-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();