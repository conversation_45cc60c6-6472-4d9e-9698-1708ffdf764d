"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/map-obj";
exports.ids = ["vendor-chunks/map-obj"];
exports.modules = {

/***/ "(rsc)/./node_modules/map-obj/index.js":
/*!***************************************!*\
  !*** ./node_modules/map-obj/index.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ mapObject),\n/* harmony export */   mapObjectSkip: () => (/* binding */ mapObjectSkip)\n/* harmony export */ });\nconst isObject = value => typeof value === 'object' && value !== null;\n\n// Customized for this use-case\nconst isObjectCustom = value =>\n\tisObject(value)\n\t&& !(value instanceof RegExp)\n\t&& !(value instanceof Error)\n\t&& !(value instanceof Date);\n\nconst mapObjectSkip = Symbol('mapObjectSkip');\n\nconst _mapObject = (object, mapper, options, isSeen = new WeakMap()) => {\n\toptions = {\n\t\tdeep: false,\n\t\ttarget: {},\n\t\t...options,\n\t};\n\n\tif (isSeen.has(object)) {\n\t\treturn isSeen.get(object);\n\t}\n\n\tisSeen.set(object, options.target);\n\n\tconst {target} = options;\n\tdelete options.target;\n\n\tconst mapArray = array => array.map(element => isObjectCustom(element) ? _mapObject(element, mapper, options, isSeen) : element);\n\tif (Array.isArray(object)) {\n\t\treturn mapArray(object);\n\t}\n\n\tfor (const [key, value] of Object.entries(object)) {\n\t\tconst mapResult = mapper(key, value, object);\n\n\t\tif (mapResult === mapObjectSkip) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet [newKey, newValue, {shouldRecurse = true} = {}] = mapResult;\n\n\t\t// Drop `__proto__` keys.\n\t\tif (newKey === '__proto__') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (options.deep && shouldRecurse && isObjectCustom(newValue)) {\n\t\t\tnewValue = Array.isArray(newValue)\n\t\t\t\t? mapArray(newValue)\n\t\t\t\t: _mapObject(newValue, mapper, options, isSeen);\n\t\t}\n\n\t\ttarget[newKey] = newValue;\n\t}\n\n\treturn target;\n};\n\nfunction mapObject(object, mapper, options) {\n\tif (!isObject(object)) {\n\t\tthrow new TypeError(`Expected an object, got \\`${object}\\` (${typeof object})`);\n\t}\n\n\treturn _mapObject(object, mapper, options);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/map-obj/index.js\n");

/***/ })

};
;