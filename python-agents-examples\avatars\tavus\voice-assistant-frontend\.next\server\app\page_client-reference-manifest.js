globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Public_Sans\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"]}],\"variableName\":\"publicSans400\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Public_Sans\",\"arguments\":[{\"weight\":\"400\",\"subsets\":[\"latin\"]}],\"variableName\":\"publicSans400\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\@livekit\\components-styles\\dist\\general\\index.css":{"id":"(app-pages-browser)/./node_modules/@livekit/components-styles/dist/general/index.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\":[],"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\app\\page":[],"D:\\Work\\Projects\\2025\\livekitdemo\\python-agents-examples\\avatars\\tavus\\voice-assistant-frontend\\app\\layout":["static/css/app/layout.css"]}}