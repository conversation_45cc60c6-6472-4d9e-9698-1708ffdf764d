"use client";

import {
  MotionCanvas,
  LayoutOrthographicCamera,
} from "framer-motion/three";
import { useThree, useFrame } from "@react-three/fiber";
import * as THREE from "three";
import { useEffect, useMemo, useState } from "react";
import { transition } from "./settings";

export function Scene({ isFullscreen }: { isFullscreen: boolean }) {
  const videoTexture = useVideoTexture("/room.mp4");

  return (
    <MotionCanvas dpr={[1, 2]}>
      <LayoutOrthographicCamera
        makeDefault
        initial={false}
        animate={
          isFullscreen
            ? {
                position: [0, 0, 1],
                zoom: 90,
              }
            : {
                position: [0, 0, 10],
                zoom: 67,
              }
        }
        transition={transition}
      />
      <Lights />
      <VideoPlane texture={videoTexture} />
    </MotionCanvas>
  );
}

function Lights() {
  const { camera } = useThree();
  useFrame(() => camera.lookAt(0, 0, 0));

  return (
    <>
      <ambientLight intensity={0.4} />
      <directionalLight intensity={1} position={[5, 5, 5]} />
    </>
  );
}

function VideoPlane({ texture }: { texture: THREE.VideoTexture }) {
  return (
    <mesh position={[0, 0, 0]}>
      <planeGeometry args={[16, 9]} />
      <meshBasicMaterial map={texture} toneMapped={false} />
    </mesh>
  );
}

function useVideoTexture(src: string): THREE.VideoTexture {
  const [video] = useState(() => {
    const v = document.createElement("video");
    v.src = src;
    v.crossOrigin = "anonymous";
    v.loop = true;
    v.muted = true;
    v.playsInline = true;
    v.autoplay = true;
    v.play();
    return v;
  });

  const texture = useMemo(() => {
    const t = new THREE.VideoTexture(video);
    t.minFilter = THREE.LinearFilter;
    t.magFilter = THREE.LinearFilter;
    t.format = THREE.RGBFormat;
    return t;
  }, [video]);

  return texture;
}
